"""
Unit tests for broker implementations.

This module contains tests for the abstract Broker interface and
concrete broker implementations like ZerodhaBroker.

To run live tests with real Zerodha credentials:
1. Set environment variables: ZERODHA_API_KEY, ZERODHA_ACCESS_TOKEN
2. Run: pytest framework/tests/test_broker.py -v

Note: Mock mode tests use real Zerodha API for market data but simulate order execution.
"""

import pytest
import os
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from framework.broker import Broker
from framework.zerodha_broker import ZerodhaBroker
from framework.models import Position, Tick


class TestAbstractBroker:
    """Test cases for the abstract Broker class."""

    def test_abstract_broker_instantiation_raises(self):
        """Test that Broker cannot be instantiated directly."""
        with pytest.raises(TypeError):
            # Attempting to instantiate abstract class should raise TypeError
            Broker()


@pytest.fixture
def mock_kite():
    """Create a mock KiteConnect instance for testing."""
    mock = MagicMock()

    # Mock profile response
    mock.profile.return_value = {"user_name": "test_user", "user_id": "TEST123"}

    # Mock instruments response
    mock.instruments.return_value = [
        {
            "instrument_token": 256265,
            "exchange_token": 1001,
            "tradingsymbol": "RELIANCE",
            "name": "RELIANCE INDUSTRIES LTD",
            "last_price": 2500.0,
            "expiry": "",
            "strike": 0.0,
            "tick_size": 0.05,
            "lot_size": 1,
            "instrument_type": "EQ",
            "segment": "NSE",
            "exchange": "NSE"
        },
        {
            "instrument_token": 408065,
            "exchange_token": 1594,
            "tradingsymbol": "INFY",
            "name": "INFOSYS LTD",
            "last_price": 1500.0,
            "expiry": "",
            "strike": 0.0,
            "tick_size": 0.05,
            "lot_size": 1,
            "instrument_type": "EQ",
            "segment": "NSE",
            "exchange": "NSE"
        }
    ]

    # Mock quote response
    mock.quote.return_value = {
        "256265": {
            "instrument_token": 256265,
            "last_price": 2500.0,
            "ohlc": {"open": 2480.0, "high": 2520.0, "low": 2470.0, "close": 2500.0},
            "depth": {
                "buy": [{"price": 2499.5, "quantity": 100}],
                "sell": [{"price": 2500.5, "quantity": 150}]
            }
        }
    }

    # Mock margins response
    mock.margins.return_value = {
        "equity": {
            "available": {"cash": 100000.0, "intraday_payin": 150000.0, "opening_balance": 100000.0},
            "utilised": {"debits": 25000.0, "span": 15000.0, "exposure": 10000.0, "option_premium": 0.0},
            "net": 175000.0
        }
    }

    # Mock positions response
    mock.positions.return_value = {
        "net": [
            {
                "tradingsymbol": "RELIANCE",
                "quantity": 100,
                "average_price": 2480.0,
                "last_price": 2500.0,
                "pnl": 2000.0
            }
        ]
    }

    # Mock order placement
    mock.place_order.return_value = "test_order_123"

    # Mock order cancellation
    mock.cancel_order.return_value = True

    # Mock orders list
    mock.orders.return_value = [
        {
            "order_id": "test_order_123",
            "tradingsymbol": "RELIANCE",
            "quantity": 100,
            "status": "COMPLETE",
            "variety": "regular"
        }
    ]

    return mock


class TestZerodhaBrokerMocked:
    """Test ZerodhaBroker with mocked KiteConnect API."""

    @patch('framework.zerodha_broker.KiteConnect')
    def test_broker_initialization_requires_credentials(self, mock_kite_class):
        """Test that broker requires API credentials."""
        # Test missing API key
        with pytest.raises(ValueError, match="API key is required"):
            ZerodhaBroker(access_token="dummy_token")

        # Test missing access token
        with pytest.raises(ValueError, match="Access token is required"):
            ZerodhaBroker(api_key="dummy_key")

    @patch('framework.zerodha_broker.KiteConnect')
    def test_broker_initialization_success(self, mock_kite_class, mock_kite):
        """Test successful broker initialization."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        assert broker.api_key == "test_key"
        assert broker.access_token == "test_token"
        assert broker.mock_orders is True
        assert broker._is_connected is False

    @patch('framework.zerodha_broker.KiteConnect')
    def test_broker_connection(self, mock_kite_class, mock_kite):
        """Test broker connection process."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        # Test connection
        broker.connect()
        assert broker._is_connected is True

        # Verify API calls were made
        mock_kite.profile.assert_called_once()
        mock_kite.instruments.assert_called()

        # Test disconnection
        broker.disconnect()
        assert broker._is_connected is False

    @patch('framework.zerodha_broker.KiteConnect')
    def test_mock_order_flow(self, mock_kite_class, mock_kite):
        """Test complete order flow in mock mode with real API data."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        # Connect to broker
        broker.connect()
        assert broker._is_connected is True

        # Place first order - should use real market price from API
        order_response = broker.place_order("RELIANCE", 100, "market")
        assert order_response["status"] == "success"
        assert "order_id" in order_response
        order_id = order_response["order_id"]

        # Verify quote API was called to get real price
        mock_kite.quote.assert_called()

        # Check that position was created
        positions = broker.get_positions()
        assert len(positions) == 1
        reliance_position = positions[0]
        assert reliance_position.symbol == "RELIANCE"
        assert reliance_position.qty == 100
        assert reliance_position.side == "long"

        # Place additional buy order
        broker.place_order("RELIANCE", 50, "market")
        positions = broker.get_positions()
        reliance_position = positions[0]
        assert reliance_position.qty == 150  # 100 + 50 = 150

        # Test sell order
        broker.place_order("RELIANCE", -30, "market")
        positions = broker.get_positions()
        reliance_position = positions[0]
        assert reliance_position.qty == 120  # 150 - 30 = 120

        # Test order cancellation - place a limit order first (which won't be filled immediately)
        limit_order_response = broker.place_order("RELIANCE", 10, "limit", 1000.0)  # Very low price
        limit_order_id = limit_order_response["order_id"]

        # This should be cancellable since it's a limit order
        cancel_result = broker.cancel_order(limit_order_id)
        assert cancel_result is True

        # Check that order status was updated
        cancelled_order = broker._orders[limit_order_id]
        assert cancelled_order.status == "cancelled"

        # Test cancelling already filled order (should fail)
        cancel_filled_result = broker.cancel_order(order_id)  # This was a market order, already filled
        assert cancel_filled_result is False

    @patch('framework.zerodha_broker.KiteConnect')
    def test_account_balance_real_data(self, mock_kite_class, mock_kite):
        """Test account balance retrieval with real API data."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        broker.connect()
        balance = broker.get_account_balance()

        # Verify real API was called
        mock_kite.margins.assert_called()

        # Check balance structure
        assert isinstance(balance, dict)
        assert "cash" in balance
        assert "margin_available" in balance
        assert "total" in balance
        assert balance["cash"] == 100000.0  # From mock response
        assert balance["total"] == 175000.0  # From mock response

    @patch('framework.zerodha_broker.KiteConnect')
    def test_option_chain_real_data(self, mock_kite_class, mock_kite):
        """Test option chain retrieval with real instrument data."""
        mock_kite_class.return_value = mock_kite

        # Add option instruments to mock response
        option_instruments = [
            {
                "instrument_token": 12345,
                "tradingsymbol": "NIFTY24JAN18000CE",
                "name": "NIFTY",
                "instrument_type": "CE",
                "strike": 18000.0,
                "expiry": "2024-01-25",
                "lot_size": 50,
                "exchange": "NFO"
            },
            {
                "instrument_token": 12346,
                "tradingsymbol": "NIFTY24JAN18000PE",
                "name": "NIFTY",
                "instrument_type": "PE",
                "strike": 18000.0,
                "expiry": "2024-01-25",
                "lot_size": 50,
                "exchange": "NFO"
            }
        ]

        # Combine with existing mock instruments
        mock_kite.instruments.return_value.extend(option_instruments)

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        broker.connect()
        option_chain = broker.get_option_chain("NIFTY")

        assert option_chain.symbol == "NIFTY"
        assert isinstance(option_chain.chain, list)
        assert len(option_chain.chain) == 2  # CE and PE

        # Check option structure
        for option in option_chain.chain:
            assert "strike" in option
            assert "option_type" in option
            assert "symbol" in option
            assert "lot_size" in option
            assert option["option_type"] in ["CE", "PE"]

    @patch('framework.zerodha_broker.KiteConnect')
    def test_get_quote_real_data(self, mock_kite_class, mock_kite):
        """Test quote retrieval with real API data."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        broker.connect()
        quotes = broker.get_quote(["RELIANCE"])

        # Verify API was called
        mock_kite.quote.assert_called()

        # Check quote structure
        assert "RELIANCE" in quotes
        reliance_quote = quotes["RELIANCE"]
        assert "last_price" in reliance_quote
        assert "ohlc" in reliance_quote
        assert reliance_quote["last_price"] == 2500.0  # From mock response

    @patch('framework.zerodha_broker.KiteConnect')
    def test_broker_not_connected_error(self, mock_kite_class, mock_kite):
        """Test that operations fail when broker is not connected."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=True
        )

        # Don't call connect()
        with pytest.raises(RuntimeError, match="Broker not connected"):
            broker.place_order("TEST", 1, "market")

        with pytest.raises(RuntimeError, match="Broker not connected"):
            broker.get_positions()

        with pytest.raises(RuntimeError, match="Broker not connected"):
            broker.get_account_balance()

    @patch('framework.zerodha_broker.KiteConnect')
    def test_live_mode_order_placement(self, mock_kite_class, mock_kite):
        """Test order placement in live mode."""
        mock_kite_class.return_value = mock_kite

        broker = ZerodhaBroker(
            api_key="test_key",
            access_token="test_token",
            mock_orders=False  # Live mode
        )

        broker.connect()

        # Place order in live mode
        order_response = broker.place_order("RELIANCE", 100, "market")

        # Verify real API was called
        mock_kite.place_order.assert_called_once()

        # Check response
        assert order_response["status"] == "success"
        assert order_response["order_id"] == "test_order_123"  # From mock

    @patch('framework.zerodha_broker.KiteConnect')
    def test_environment_variable_loading(self, mock_kite_class, mock_kite):
        """Test loading credentials from environment variables."""
        mock_kite_class.return_value = mock_kite

        # Set environment variables
        os.environ["ZERODHA_API_KEY"] = "env_api_key"
        os.environ["ZERODHA_ACCESS_TOKEN"] = "env_access_token"

        try:
            # Create broker without explicit credentials
            broker = ZerodhaBroker(mock_orders=True)

            # Check that credentials were loaded from environment
            assert broker.api_key == "env_api_key"
            assert broker.access_token == "env_access_token"

        finally:
            # Clean up environment variables
            if "ZERODHA_API_KEY" in os.environ:
                del os.environ["ZERODHA_API_KEY"]
            if "ZERODHA_ACCESS_TOKEN" in os.environ:
                del os.environ["ZERODHA_ACCESS_TOKEN"]


@pytest.mark.skipif(
    not os.getenv("ZERODHA_API_KEY") or not os.getenv("ZERODHA_ACCESS_TOKEN"),
    reason="Live tests require ZERODHA_API_KEY and ZERODHA_ACCESS_TOKEN environment variables"
)
class TestZerodhaBrokerLive:
    """
    Live tests with real Zerodha API (only run if credentials are available).

    These tests make actual API calls but do not place real orders.
    Set ZERODHA_API_KEY and ZERODHA_ACCESS_TOKEN environment variables to run.

    WARNING: These tests use real API calls and may consume API rate limits.
    """

    def setup_method(self):
        """Setup for live tests."""
        self.broker = ZerodhaBroker(mock_orders=True)  # Mock orders but real data

    def test_live_connection_and_data_retrieval(self):
        """Test real connection and data retrieval without placing orders."""
        # This test only runs if credentials are available (due to skipif decorator)

        # Test connection with real API
        self.broker.connect()
        assert self.broker._is_connected is True
        assert self.broker.kite is not None

        # Test getting real account balance
        balance = self.broker.get_account_balance()
        assert isinstance(balance, dict)
        assert "cash" in balance
        assert "total" in balance
        print(f"Account balance: ₹{balance.get('cash', 0):,.2f}")

        # Test getting real positions
        positions = self.broker.get_positions()
        assert isinstance(positions, list)
        print(f"Found {len(positions)} positions")

        # Test getting real quotes for common symbols
        try:
            quotes = self.broker.get_quote(["RELIANCE", "TCS"])
            assert isinstance(quotes, dict)
            for symbol, quote in quotes.items():
                if quote:  # Only check if quote data is available
                    assert "last_price" in quote
                    print(f"{symbol}: ₹{quote['last_price']}")
        except Exception as e:
            print(f"Quote test failed (may be due to market hours): {e}")

        # Test getting option chain for NIFTY
        try:
            option_chain = self.broker.get_option_chain("NIFTY")
            assert isinstance(option_chain.chain, list)
            print(f"NIFTY option chain: {len(option_chain.chain)} contracts")
        except Exception as e:
            print(f"Option chain test failed: {e}")

        # Test mock order placement (should not hit real API)
        order_response = self.broker.place_order("RELIANCE", 1, "market")
        assert order_response["status"] == "success"
        print(f"Mock order placed: {order_response['order_id']}")

        # Verify position was updated locally (mock mode)
        positions_after = self.broker.get_positions()
        mock_positions = [p for p in positions_after if p.symbol == "RELIANCE"]
        if mock_positions:
            print(f"Mock position created: {mock_positions[0].symbol} {mock_positions[0].qty}")

        # Clean up
        self.broker.disconnect()
        assert self.broker._is_connected is False


if __name__ == '__main__':
    pytest.main([__file__])
