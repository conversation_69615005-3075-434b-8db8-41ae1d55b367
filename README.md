# Algorithmic Trading Framework

A comprehensive Python framework for developing and executing algorithmic trading strategies with support for multiple brokers, real-time market data processing, and backtesting capabilities.

## Features

- **Modular Architecture**: Clean separation between brokers, strategies, and market data handling
- **Multiple Broker Support**: Currently supports Zerodha (KiteConnect) with extensible broker interface
- **Real-time Market Data**: Efficient market data processing with tick, bar, quote, and orderbook support
- **Strategy Development**: Abstract base classes for easy strategy development
- **Technical Indicators**: Built-in technical indicators with TA-Lib integration
- **Backtesting**: Historical strategy testing capabilities (coming soon)
- **Paper Trading**: Risk-free strategy testing with simulated orders (coming soon)
- **Configuration Management**: Flexible configuration with environment variables and config files
- **Comprehensive Logging**: Detailed logging with configurable levels and outputs
- **CLI Interface**: Command-line interface for running strategies and backtests

## Installation

### Prerequisites

- Python 3.8 or higher
- TA-Lib library (for technical indicators)

### Install TA-Lib

#### On Ubuntu/Debian:
```bash
sudo apt-get install build-essential
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure --prefix=/usr
make
sudo make install
```

#### On macOS:
```bash
brew install ta-lib
```

#### On Windows:
Download and install from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib

### Install the Framework

```bash
# Clone the repository
git clone https://github.com/algotrade-framework/algotrade-framework.git
cd algotrade-framework

# Install dependencies
pip install -r requirements.txt

# Install the framework
pip install -e .
```

## Quick Start

### 1. Configuration

Create a `.env` file in the project root:

```env
# Zerodha Configuration
ZERODHA_API_KEY=your_api_key
ZERODHA_ACCESS_TOKEN=your_access_token
ZERODHA_API_SECRET=your_api_secret

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/algotrade.log

# Strategy Configuration
STRATEGY_MA_CROSSOVER_FAST_PERIOD=10
STRATEGY_MA_CROSSOVER_SLOW_PERIOD=20
STRATEGY_MA_CROSSOVER_SYMBOL=RELIANCE
STRATEGY_MA_CROSSOVER_QUANTITY=100
```

### 2. Run a Strategy

```bash
# Run moving average crossover strategy
python cli.py run --strategy ma_crossover --symbol RELIANCE --quantity 100

# Run with custom configuration
python cli.py run --strategy ma_crossover --symbol TCS --quantity 50 --config config.json
```

### 3. Backtest a Strategy

```bash
# Backtest strategy (coming soon)
python cli.py backtest --strategy ma_crossover --start-date 2023-01-01 --end-date 2023-12-31 --symbol NIFTY
```

### 4. Paper Trading

```bash
# Paper trade strategy (coming soon)
python cli.py papertrade --strategy ma_crossover --symbol BANKNIFTY --capital 100000
```

## Framework Architecture

### Core Components

1. **Broker Interface** (`framework/broker.py`): Abstract interface for broker implementations
2. **Market Data Handler** (`framework/blotter.py`): Real-time market data processing
3. **Algorithm Base** (`framework/algo.py`): Abstract base class for trading strategies
4. **Instrument Model** (`framework/instrument.py`): Data model for financial instruments

### Broker Implementations

- **Zerodha** (`framework/brokers/zerodha.py`): KiteConnect API integration

### Strategies

- **Moving Average Crossover** (`framework/strategies/crossover.py`): Simple MA crossover strategy

### Technical Indicators

- Simple Moving Average (SMA)
- Exponential Moving Average (EMA)
- Relative Strength Index (RSI)
- MACD
- Bollinger Bands
- Stochastic Oscillator

## Developing Custom Strategies

### 1. Create a Strategy Class

```python
from framework.algo import Algo

class MyStrategy(Algo):
    def __init__(self, name="MyStrategy", config=None):
        super().__init__(name, config)
        # Initialize strategy parameters
    
    async def on_start(self):
        # Strategy initialization logic
        pass
    
    async def on_stop(self):
        # Strategy cleanup logic
        pass
    
    async def on_tick(self, tick_data):
        # Handle tick data
        pass
    
    async def on_bar(self, bar_data):
        # Handle bar data
        pass
    
    async def on_fill(self, fill_data):
        # Handle order fills
        pass
    
    async def on_quote(self, quote_data):
        # Handle quote updates
        pass
    
    async def on_orderbook(self, orderbook_data):
        # Handle orderbook updates
        pass
```

### 2. Register Strategy in CLI

Add your strategy to the CLI choices in `cli.py`:

```python
# In create_parser() function
run_parser.add_argument('--strategy', required=True, 
                       choices=['ma_crossover', 'my_strategy'], 
                       help='Strategy to run')
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test file
pytest framework/tests/test_broker.py

# Run with coverage
pytest --cov=framework
```

## Project Structure

```
algotrade-framework/
├── framework/
│   ├── __init__.py
│   ├── algo.py                 # Abstract algorithm base class
│   ├── blotter.py             # Market data handler
│   ├── broker.py              # Abstract broker interface
│   ├── instrument.py          # Instrument data model
│   ├── brokers/
│   │   ├── __init__.py
│   │   └── zerodha.py         # Zerodha broker implementation
│   ├── indicators/
│   │   ├── __init__.py
│   │   └── indicators.py      # Technical indicators
│   ├── strategies/
│   │   ├── __init__.py
│   │   └── crossover.py       # MA crossover strategy
│   ├── tests/
│   │   ├── __init__.py
│   │   ├── test_broker.py
│   │   ├── test_blotter.py
│   │   ├── test_algo.py
│   │   └── test_strategy.py
│   └── utils/
│       ├── __init__.py
│       ├── config_loader.py   # Configuration management
│       └── logging_config.py  # Logging setup
├── cli.py                     # Command line interface
├── requirements.txt
├── setup.py
├── README.md
└── .gitignore
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

This software is for educational and research purposes only. Use at your own risk. The authors are not responsible for any financial losses incurred through the use of this software.

## Support

- Documentation: [Coming Soon]
- Issues: [GitHub Issues](https://github.com/algotrade-framework/algotrade-framework/issues)
- Discussions: [GitHub Discussions](https://github.com/algotrade-framework/algotrade-framework/discussions)

## Roadmap

- [ ] Complete backtesting engine
- [ ] Paper trading implementation
- [ ] Additional broker integrations (Interactive Brokers, Alpaca)
- [ ] Web-based dashboard
- [ ] More technical indicators
- [ ] Machine learning integration
- [ ] Risk management tools
- [ ] Portfolio optimization
- [ ] Real-time performance monitoring
