"""
ZerodhaBroker implements <PERSON><PERSON><PERSON> using Kite Connect.
- Live mode: Real orders placed through Zerodha API
- Mock mode: Real market data from Zerodha API, but orders are simulated locally
- Always requires valid API credentials for real data access
Environment variables:
  ZERODHA_API_KEY, ZERODHA_ACCESS_TOKEN
Docs: https://kite.trade/docs/pykiteconnect/v4/
"""

import os
import uuid
import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dotenv import load_dotenv

from .broker import Broker
from .models import Position, Order, OptionChain, Instrument, Tick

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

try:
    from kiteconnect import KiteConnect, KiteTicker
except ImportError:
    logger.error("kiteconnect package not found. Install with: pip install kiteconnect")
    KiteConnect = None
    KiteTicker = None


class ZerodhaBroker(Broker):
    """
    Zerodha broker implementation using KiteConnect API.

    Always uses real Zerodha API for market data and account information.
    Mock mode only simulates order execution while using real market data.
    """

    def __init__(self, api_key: Optional[str] = None, access_token: Optional[str] = None,
                 mock_orders: bool = True):
        """
        Initialize ZerodhaBroker with credentials and mode.

        Args:
            api_key: Zerodha API key (loads from ZERODHA_API_KEY env var if None)
            access_token: Zerodha access token (loads from ZERODHA_ACCESS_TOKEN env var if None)
            mock_orders: If True, simulate order execution but use real market data
        """
        if KiteConnect is None:
            raise ImportError("kiteconnect package is required. Install with: pip install kiteconnect")

        # Load credentials from environment if not provided
        self.api_key = api_key or os.getenv("ZERODHA_API_KEY")
        self.access_token = access_token or os.getenv("ZERODHA_ACCESS_TOKEN")
        self.mock_orders = mock_orders

        # Validate required credentials
        if not self.api_key:
            raise ValueError("API key is required. Provide api_key parameter or set ZERODHA_API_KEY environment variable")
        if not self.access_token:
            raise ValueError("Access token is required. Provide access_token parameter or set ZERODHA_ACCESS_TOKEN environment variable")

        # Initialize KiteConnect instance
        self.kite = KiteConnect(api_key=self.api_key)
        self.kite.set_access_token(self.access_token)

        # Initialize state
        self.ticker = None  # Will hold KiteTicker instance for websocket
        self._orders: Dict[str, Order] = {}  # In-memory order storage for mock mode
        self._positions: Dict[str, Position] = {}  # In-memory position storage for mock mode
        self._instruments: Dict[str, Dict] = {}  # Cache for instrument data
        self._instrument_tokens: Dict[str, int] = {}  # Symbol to token mapping
        self._token_symbols: Dict[int, str] = {}  # Token to symbol mapping
        self._is_connected = False
        self._ticker_thread = None
        self._tick_callbacks: List[Callable[[Tick], None]] = []

        logger.info(f"ZerodhaBroker initialized in {'mock orders' if mock_orders else 'live'} mode")
    
    def connect(self) -> None:
        """
        Establish connection to Zerodha API and load instrument data.

        Always connects to real Zerodha API for market data access.
        """
        try:
            # Test connection by fetching user profile
            profile = self.kite.profile()
            logger.info(f"Connected to Zerodha API for user: {profile.get('user_name', 'Unknown')}")

            # Load and cache instrument data for symbol-token mapping
            self._load_instruments()

            # Initialize mock state if in mock mode
            if self.mock_orders:
                self._orders.clear()
                self._positions.clear()
                logger.info("Mock order mode enabled - orders will be simulated")

            self._is_connected = True

        except Exception as e:
            logger.error(f"Failed to connect to Zerodha API: {e}")
            raise RuntimeError(f"Connection failed: {e}")

    def _load_instruments(self) -> None:
        """
        Load and cache instrument data from Zerodha API.
        Creates mapping between symbols and instrument tokens for websocket.
        """
        try:
            logger.info("Loading instrument data from Zerodha...")

            # Get instruments for major exchanges
            exchanges = ["NSE", "BSE", "NFO", "BFO", "CDS", "MCX"]

            for exchange in exchanges:
                try:
                    instruments = self.kite.instruments(exchange)
                    for instrument in instruments:
                        symbol = instrument["tradingsymbol"]
                        token = instrument["instrument_token"]

                        # Store instrument data
                        self._instruments[symbol] = instrument
                        self._instrument_tokens[symbol] = token
                        self._token_symbols[token] = symbol

                except Exception as e:
                    logger.warning(f"Failed to load instruments for {exchange}: {e}")
                    continue

            logger.info(f"Loaded {len(self._instruments)} instruments from Zerodha")

        except Exception as e:
            logger.error(f"Failed to load instruments: {e}")
            # Don't fail connection if instruments can't be loaded
            pass
    
    def disconnect(self) -> None:
        """
        Disconnect from Zerodha API and clean up resources.
        """
        try:
            # Stop ticker if running
            if self.ticker:
                self.ticker.close()
                self.ticker = None

            # Stop ticker thread if running
            if self._ticker_thread and self._ticker_thread.is_alive():
                self._ticker_thread.join(timeout=5)
                self._ticker_thread = None

            self._is_connected = False
            logger.info("Disconnected from Zerodha API")

        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
            self._is_connected = False
    
    def place_order(self, symbol: str, qty: int, order_type: str = "market",
                   price: Optional[float] = None, **kwargs) -> Dict[str, Any]:
        """
        Place an order with Zerodha.

        In mock mode: Simulates order execution using real market price from API
        In live mode: Places actual order through Zerodha API

        Args:
            symbol: Trading symbol
            qty: Order quantity (positive for buy, negative for sell)
            order_type: Order type ("market", "limit", etc.)
            price: Price for limit orders
            **kwargs: Additional parameters (exchange, product, etc.)

        Returns:
            Dict with order response containing status and order_id
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        side = "buy" if qty > 0 else "sell"
        abs_qty = abs(qty)

        if self.mock_orders:
            # Mock mode: simulate order but use real market data for fill price
            order_id = uuid.uuid4().hex

            # Get real market price from Zerodha API for realistic simulation
            fill_price = self._get_market_price(symbol, side, price, order_type)

            # Create mock order
            order = Order(
                order_id=order_id,
                symbol=symbol,
                qty=qty,
                order_type=order_type,
                status="filled" if order_type == "market" else "open",
                price=fill_price if order_type == "market" else price,
                filled_at=datetime.now() if order_type == "market" else None
            )
            self._orders[order_id] = order

            # Update position immediately for market orders (simulate instant fill)
            if order_type == "market":
                self._update_position_on_fill(symbol, abs_qty, fill_price, side)
                logger.info(f"Mock order filled: {symbol} {side} {abs_qty} @ ₹{fill_price}")
            else:
                logger.info(f"Mock order placed: {symbol} {side} {abs_qty} @ ₹{price} (limit)")

            return {"status": "success", "order_id": order_id}

        else:
            # Live mode: place real order through Zerodha API
            try:
                # Extract parameters
                exchange = kwargs.get("exchange", "NSE")
                product = kwargs.get("product", "MIS")
                variety = kwargs.get("variety", "regular")

                # Map to Kite constants
                transaction_type = self.kite.TRANSACTION_TYPE_BUY if qty > 0 else self.kite.TRANSACTION_TYPE_SELL
                kite_order_type = self._map_order_type(order_type)
                kite_exchange = self._map_exchange(exchange)
                kite_product = self._map_product(product)
                kite_variety = self._map_variety(variety)

                order_id = self.kite.place_order(
                    variety=kite_variety,
                    exchange=kite_exchange,
                    tradingsymbol=symbol,
                    transaction_type=transaction_type,
                    quantity=abs_qty,
                    product=kite_product,
                    order_type=kite_order_type,
                    price=price
                )

                logger.info(f"Live order placed: {order_id} - {symbol} {side} {abs_qty}")
                return {"status": "success", "order_id": order_id}

            except Exception as e:
                logger.error(f"Failed to place order: {e}")
                return {"status": "error", "message": str(e)}
    
    def _get_market_price(self, symbol: str, side: str, limit_price: Optional[float], order_type: str) -> float:
        """
        Get realistic market price for order simulation using real Zerodha data.

        Args:
            symbol: Trading symbol
            side: Order side ("buy" or "sell")
            limit_price: Limit price if provided
            order_type: Order type

        Returns:
            Realistic fill price based on current market data
        """
        try:
            # Get real quote from Zerodha API
            if symbol in self._instrument_tokens:
                token = self._instrument_tokens[symbol]
                quote = self.kite.quote([token])

                if token in quote:
                    market_data = quote[token]
                    last_price = market_data.get("last_price", 0)

                    if order_type == "market":
                        # For market orders, use last price with small spread
                        if side == "buy":
                            return last_price * 1.001  # Small premium for buy
                        else:
                            return last_price * 0.999  # Small discount for sell
                    else:
                        # For limit orders, use the limit price
                        return limit_price or last_price

            # Fallback: use limit price or default
            return limit_price or 100.0

        except Exception as e:
            logger.warning(f"Failed to get market price for {symbol}: {e}")
            return limit_price or 100.0

    def _map_order_type(self, order_type: str) -> str:
        """Map order type to Kite constants."""
        mapping = {
            "market": self.kite.ORDER_TYPE_MARKET,
            "limit": self.kite.ORDER_TYPE_LIMIT,
            "sl": self.kite.ORDER_TYPE_SL,
            "sl-m": self.kite.ORDER_TYPE_SLM
        }
        return mapping.get(order_type.lower(), self.kite.ORDER_TYPE_MARKET)

    def _map_exchange(self, exchange: str) -> str:
        """Map exchange to Kite constants."""
        mapping = {
            "nse": self.kite.EXCHANGE_NSE,
            "bse": self.kite.EXCHANGE_BSE,
            "nfo": self.kite.EXCHANGE_NFO,
            "bfo": self.kite.EXCHANGE_BFO,
            "mcx": self.kite.EXCHANGE_MCX
        }
        return mapping.get(exchange.upper(), self.kite.EXCHANGE_NSE)

    def _map_product(self, product: str) -> str:
        """Map product to Kite constants."""
        mapping = {
            "mis": self.kite.PRODUCT_MIS,
            "cnc": self.kite.PRODUCT_CNC,
            "nrml": self.kite.PRODUCT_NRML
        }
        return mapping.get(product.upper(), self.kite.PRODUCT_MIS)

    def _map_variety(self, variety: str) -> str:
        """Map variety to Kite constants."""
        mapping = {
            "regular": self.kite.VARIETY_REGULAR,
            "amo": self.kite.VARIETY_AMO,
            "co": self.kite.VARIETY_CO,
            "iceberg": self.kite.VARIETY_ICEBERG
        }
        return mapping.get(variety.lower(), self.kite.VARIETY_REGULAR)

    def _update_position_on_fill(self, symbol: str, qty: int, fill_price: float, side: str) -> None:
        """
        Update in-memory position when an order is filled (mock mode only).

        Args:
            symbol: Trading symbol
            qty: Filled quantity (positive)
            fill_price: Fill price
            side: Fill side ("buy" or "sell")
        """
        if symbol not in self._positions:
            # Create new position
            initial_qty = qty if side == "buy" else -qty
            self._positions[symbol] = Position(
                symbol=symbol,
                qty=initial_qty,
                avg_price=fill_price,
                side="long" if side == "buy" else "short",
                updated_at=datetime.now()
            )
        else:
            # Update existing position using the position's update logic
            self._positions[symbol].update_on_fill(qty, fill_price, side)
    
    def cancel_order(self, order_id: str) -> bool:
        """
        Cancel an existing order.

        Args:
            order_id: Order ID to cancel

        Returns:
            True if cancellation successful
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        if self.mock_orders:
            # Mock mode: mark order as cancelled in local storage
            if order_id in self._orders:
                order = self._orders[order_id]
                if order.status in ["open", "pending"]:
                    order.status = "cancelled"
                    logger.info(f"Mock order cancelled: {order_id}")
                    return True
                else:
                    logger.warning(f"Cannot cancel order {order_id} with status: {order.status}")
                    return False
            else:
                logger.warning(f"Order {order_id} not found in mock orders")
                return False

        else:
            # Live mode: cancel real order through Kite API
            try:
                # Get order details first to determine variety
                orders = self.kite.orders()
                target_order = None
                for order in orders:
                    if order["order_id"] == order_id:
                        target_order = order
                        break

                if not target_order:
                    logger.error(f"Order {order_id} not found")
                    return False

                variety = target_order.get("variety", "regular")
                kite_variety = self._map_variety(variety)

                self.kite.cancel_order(variety=kite_variety, order_id=order_id)
                logger.info(f"Live order cancelled: {order_id}")
                return True

            except Exception as e:
                logger.error(f"Failed to cancel order {order_id}: {e}")
                return False
    
    def get_positions(self) -> List[Position]:
        """
        Retrieve current positions.

        In mock mode: Returns simulated positions from local storage
        In live mode: Fetches real positions from Zerodha API

        Returns:
            List of Position objects
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        if self.mock_orders:
            # Mock mode: return simulated positions from local storage
            return list(self._positions.values())

        else:
            # Live mode: fetch real positions from Zerodha API
            try:
                positions_data = self.kite.positions()
                positions = []

                # Process net positions (day + overnight combined)
                for pos_data in positions_data.get("net", []):
                    quantity = pos_data.get("quantity", 0)
                    if quantity != 0:  # Only include non-zero positions
                        avg_price = pos_data.get("average_price", 0.0)
                        if avg_price == 0:
                            avg_price = pos_data.get("last_price", 0.0)

                        position = Position(
                            symbol=pos_data["tradingsymbol"],
                            qty=quantity,
                            avg_price=avg_price,
                            side="long" if quantity > 0 else "short",
                            updated_at=datetime.now()
                        )
                        positions.append(position)

                logger.info(f"Retrieved {len(positions)} positions from Zerodha")
                return positions

            except Exception as e:
                logger.error(f"Failed to get positions: {e}")
                return []

    def get_account_balance(self) -> Dict[str, Any]:
        """
        Retrieve account balance and margin information.

        Always fetches real balance data from Zerodha API.

        Returns:
            Dictionary containing balance details
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        try:
            # Always get real balance data from Zerodha API
            margins = self.kite.margins()

            # Extract equity segment margins
            equity_margins = margins.get("equity", {})
            available = equity_margins.get("available", {})
            utilised = equity_margins.get("utilised", {})

            balance_data = {
                "cash": available.get("cash", 0.0),
                "margin_available": available.get("intraday_payin", 0.0),
                "margin_used": utilised.get("debits", 0.0),
                "total": equity_margins.get("net", 0.0),
                "opening_balance": equity_margins.get("available", {}).get("opening_balance", 0.0),
                "payin": equity_margins.get("available", {}).get("intraday_payin", 0.0),
                "span": utilised.get("span", 0.0),
                "exposure": utilised.get("exposure", 0.0),
                "option_premium": utilised.get("option_premium", 0.0)
            }

            logger.info(f"Retrieved account balance: Cash=₹{balance_data['cash']:,.2f}, Total=₹{balance_data['total']:,.2f}")
            return balance_data

        except Exception as e:
            logger.error(f"Failed to get account balance: {e}")
            return {
                "cash": 0.0,
                "margin_available": 0.0,
                "margin_used": 0.0,
                "total": 0.0,
                "opening_balance": 0.0,
                "payin": 0.0,
                "span": 0.0,
                "exposure": 0.0,
                "option_premium": 0.0
            }

    def get_option_chain(self, symbol: str) -> OptionChain:
        """
        Retrieve option chain data for a given underlying symbol.

        Always fetches real option chain data from Zerodha API.

        Args:
            symbol: Underlying symbol (e.g., "NIFTY", "BANKNIFTY")

        Returns:
            OptionChain object containing real option contracts
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        try:
            # Get real option chain data from Zerodha API
            option_contracts = []

            # Filter cached instruments for options of the given underlying
            for instrument_symbol, instrument_data in self._instruments.items():
                name = instrument_data.get("name", "")
                instrument_type = instrument_data.get("instrument_type", "")

                # Check if this is an option for the requested underlying
                if (name.startswith(symbol) and instrument_type in ["CE", "PE"]):
                    option_contracts.append({
                        "strike": instrument_data.get("strike", 0),
                        "expiry": instrument_data.get("expiry", ""),
                        "option_type": instrument_type,
                        "symbol": instrument_data.get("tradingsymbol", ""),
                        "lot_size": instrument_data.get("lot_size", 1),
                        "exchange": instrument_data.get("exchange", ""),
                        "instrument_token": instrument_data.get("instrument_token", 0)
                    })

            # Sort by strike price and expiry
            option_contracts.sort(key=lambda x: (x["expiry"], x["strike"]))

            logger.info(f"Retrieved {len(option_contracts)} option contracts for {symbol}")
            return OptionChain(symbol=symbol, chain=option_contracts)

        except Exception as e:
            logger.error(f"Failed to get option chain for {symbol}: {e}")
            return OptionChain(symbol=symbol, chain=[])

    def start_market_ticker(self, symbols: List[str], on_tick: Callable[[Tick], None]) -> None:
        """
        Start real-time market data feed for given symbols.

        Always uses real Zerodha websocket data through KiteTicker.

        Args:
            symbols: List of symbols to subscribe to
            on_tick: Callback function to handle incoming tick data
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        # Store callback for use in ticker
        self._tick_callbacks.append(on_tick)

        try:
            # Get instrument tokens for the requested symbols
            instrument_tokens = []
            symbol_token_map = {}

            for symbol in symbols:
                if symbol in self._instrument_tokens:
                    token = self._instrument_tokens[symbol]
                    instrument_tokens.append(token)
                    symbol_token_map[token] = symbol
                else:
                    logger.warning(f"Symbol {symbol} not found in instrument list")

            if not instrument_tokens:
                logger.error("No valid instrument tokens found for symbols")
                return

            # Initialize KiteTicker
            self.ticker = KiteTicker(self.api_key, self.access_token)

            def on_ticks(ws, ticks):
                """Handle incoming ticks from KiteTicker"""
                for tick_data in ticks:
                    try:
                        token = tick_data.get("instrument_token")
                        symbol = symbol_token_map.get(token, str(token))

                        # Extract bid/ask from depth data
                        depth = tick_data.get("depth", {})
                        buy_orders = depth.get("buy", [])
                        sell_orders = depth.get("sell", [])

                        bid = buy_orders[0].get("price") if buy_orders else None
                        ask = sell_orders[0].get("price") if sell_orders else None

                        tick = Tick(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            last_price=tick_data.get("last_price", 0.0),
                            bid=bid,
                            ask=ask,
                            volume=tick_data.get("volume_traded", 0)
                        )

                        # Call all registered callbacks
                        for callback in self._tick_callbacks:
                            try:
                                callback(tick)
                            except Exception as e:
                                logger.error(f"Error in tick callback: {e}")

                    except Exception as e:
                        logger.error(f"Error processing tick data: {e}")

            def on_connect(ws, response):
                """Handle websocket connection"""
                logger.info(f"KiteTicker connected, subscribing to {len(instrument_tokens)} instruments")
                # Subscribe to the instrument tokens
                ws.subscribe(instrument_tokens)
                # Set mode to get full market depth
                ws.set_mode(ws.MODE_FULL, instrument_tokens)

            def on_close(ws, code, reason):
                """Handle websocket disconnection"""
                logger.warning(f"KiteTicker disconnected: {code} - {reason}")
                # TODO: Implement automatic reconnection logic

            def on_error(ws, code, reason):
                """Handle websocket errors"""
                logger.error(f"KiteTicker error: {code} - {reason}")

            # Wire up callbacks
            self.ticker.on_ticks = on_ticks
            self.ticker.on_connect = on_connect
            self.ticker.on_close = on_close
            self.ticker.on_error = on_error

            # Start ticker in separate thread to avoid blocking
            def run_ticker():
                try:
                    self.ticker.connect(threaded=True)
                except Exception as e:
                    logger.error(f"Failed to start ticker: {e}")

            self._ticker_thread = threading.Thread(target=run_ticker, daemon=True)
            self._ticker_thread.start()

            logger.info(f"Started real-time ticker for symbols: {symbols}")

        except Exception as e:
            logger.error(f"Failed to start market ticker: {e}")
            raise RuntimeError(f"Failed to start market ticker: {e}")

    def get_quote(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get real-time quotes for given symbols.

        Args:
            symbols: List of symbols to get quotes for

        Returns:
            Dictionary mapping symbols to quote data
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        try:
            # Get instrument tokens for symbols
            tokens = []
            token_symbol_map = {}

            for symbol in symbols:
                if symbol in self._instrument_tokens:
                    token = self._instrument_tokens[symbol]
                    tokens.append(token)
                    token_symbol_map[token] = symbol

            if not tokens:
                logger.warning("No valid tokens found for symbols")
                return {}

            # Get quotes from Zerodha API
            quotes = self.kite.quote(tokens)

            # Convert to symbol-based mapping
            result = {}
            for token, quote_data in quotes.items():
                symbol = token_symbol_map.get(int(token), str(token))
                result[symbol] = quote_data

            return result

        except Exception as e:
            logger.error(f"Failed to get quotes: {e}")
            return {}

    def get_historical_data(self, symbol: str, from_date: datetime, to_date: datetime,
                           interval: str = "day") -> List[Dict[str, Any]]:
        """
        Get historical OHLC data for a symbol.

        Args:
            symbol: Trading symbol
            from_date: Start date
            to_date: End date
            interval: Data interval (minute, day, etc.)

        Returns:
            List of OHLC data dictionaries
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        try:
            if symbol not in self._instrument_tokens:
                logger.error(f"Symbol {symbol} not found")
                return []

            token = self._instrument_tokens[symbol]

            # Get historical data from Zerodha API
            historical_data = self.kite.historical_data(
                instrument_token=token,
                from_date=from_date,
                to_date=to_date,
                interval=interval
            )

            logger.info(f"Retrieved {len(historical_data)} historical records for {symbol}")
            return historical_data

        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {e}")
            return []

    def get_orders(self) -> List[Dict[str, Any]]:
        """
        Get all orders (live mode) or mock orders (mock mode).

        Returns:
            List of order dictionaries
        """
        if not self._is_connected:
            raise RuntimeError("Broker not connected. Call connect() first.")

        if self.mock_orders:
            # Return mock orders as dictionaries
            return [order.to_dict() for order in self._orders.values()]
        else:
            # Get real orders from Zerodha API
            try:
                orders = self.kite.orders()
                logger.info(f"Retrieved {len(orders)} orders from Zerodha")
                return orders
            except Exception as e:
                logger.error(f"Failed to get orders: {e}")
                return []
