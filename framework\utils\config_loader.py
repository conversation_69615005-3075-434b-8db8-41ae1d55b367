"""
Configuration loader for the algorithmic trading framework.

This module provides functionality to load configuration from environment
variables, configuration files, and other sources using python-dotenv.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path

try:
    from dotenv import load_dotenv
except ImportError:
    load_dotenv = None


logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    Configuration loader that supports environment variables and config files.
    
    This class provides a centralized way to load configuration from various
    sources including .env files, environment variables, and JSON config files.
    """
    
    def __init__(self, env_file: Optional[str] = None, config_file: Optional[str] = None):
        """
        Initialize the configuration loader.
        
        Args:
            env_file: Path to .env file (optional)
            config_file: Path to JSON config file (optional)
        """
        self.env_file = env_file
        self.config_file = config_file
        self.config = {}
        
        # Load configuration from various sources
        self._load_env_file()
        self._load_config_file()
        self._load_environment_variables()
    
    def _load_env_file(self) -> None:
        """Load environment variables from .env file."""
        if not load_dotenv:
            logger.warning("python-dotenv not installed, skipping .env file loading")
            return
        
        env_path = self.env_file or '.env'
        if os.path.exists(env_path):
            load_dotenv(env_path)
            logger.info(f"Loaded environment variables from {env_path}")
        else:
            logger.debug(f"No .env file found at {env_path}")
    
    def _load_config_file(self) -> None:
        """Load configuration from JSON config file."""
        if not self.config_file:
            return
        
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    file_config = json.load(f)
                self.config.update(file_config)
                logger.info(f"Loaded configuration from {config_path}")
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error loading config file {config_path}: {e}")
        else:
            logger.debug(f"No config file found at {config_path}")
    
    def _load_environment_variables(self) -> None:
        """Load relevant environment variables."""
        # Define environment variable prefixes to look for
        prefixes = ['ALGO_', 'BROKER_', 'ZERODHA_', 'TRADING_']
        
        for key, value in os.environ.items():
            for prefix in prefixes:
                if key.startswith(prefix):
                    # Convert environment variable to config key
                    config_key = key.lower()
                    self.config[config_key] = self._parse_env_value(value)
                    break
    
    def _parse_env_value(self, value: str) -> Union[str, int, float, bool]:
        """
        Parse environment variable value to appropriate type.
        
        Args:
            value: String value from environment variable
            
        Returns:
            Parsed value with appropriate type
        """
        # Try to parse as boolean
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Try to parse as integer
        try:
            return int(value)
        except ValueError:
            pass
        
        # Try to parse as float
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        # First check config dictionary
        if key in self.config:
            return self.config[key]
        
        # Then check environment variables (case insensitive)
        env_key = key.upper()
        env_value = os.getenv(env_key)
        if env_value is not None:
            return self._parse_env_value(env_value)
        
        return default
    
    def get_broker_config(self, broker_name: str) -> Dict[str, Any]:
        """
        Get broker-specific configuration.
        
        Args:
            broker_name: Name of the broker (e.g., 'zerodha')
            
        Returns:
            Dictionary containing broker configuration
        """
        broker_config = {}
        prefix = f"{broker_name.lower()}_"
        
        # Get from config dictionary
        for key, value in self.config.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):]
                broker_config[config_key] = value
        
        # Get from environment variables
        env_prefix = f"{broker_name.upper()}_"
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower()
                broker_config[config_key] = self._parse_env_value(value)
        
        return broker_config
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """
        Get strategy-specific configuration.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            Dictionary containing strategy configuration
        """
        strategy_config = {}
        prefix = f"strategy_{strategy_name.lower()}_"
        
        # Get from config dictionary
        for key, value in self.config.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):]
                strategy_config[config_key] = value
        
        # Get from environment variables
        env_prefix = f"STRATEGY_{strategy_name.upper()}_"
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower()
                strategy_config[config_key] = self._parse_env_value(value)
        
        return strategy_config
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Configuration value
        """
        self.config[key] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        Update configuration with dictionary.
        
        Args:
            config_dict: Dictionary of configuration values
        """
        self.config.update(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Get all configuration as dictionary.
        
        Returns:
            Dictionary containing all configuration
        """
        return self.config.copy()
    
    def save_to_file(self, file_path: str) -> None:
        """
        Save current configuration to JSON file.
        
        Args:
            file_path: Path to save configuration file
        """
        try:
            with open(file_path, 'w') as f:
                json.dump(self.config, f, indent=2, default=str)
            logger.info(f"Configuration saved to {file_path}")
        except IOError as e:
            logger.error(f"Error saving configuration to {file_path}: {e}")


# Global configuration instance
_config_loader = None


def get_config_loader(env_file: Optional[str] = None, 
                     config_file: Optional[str] = None) -> ConfigLoader:
    """
    Get global configuration loader instance.
    
    Args:
        env_file: Path to .env file (optional)
        config_file: Path to JSON config file (optional)
        
    Returns:
        ConfigLoader instance
    """
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ConfigLoader(env_file, config_file)
    
    return _config_loader


def load_config(env_file: Optional[str] = None, 
               config_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration and return as dictionary.
    
    Args:
        env_file: Path to .env file (optional)
        config_file: Path to JSON config file (optional)
        
    Returns:
        Dictionary containing all configuration
    """
    config_loader = get_config_loader(env_file, config_file)
    return config_loader.to_dict()
