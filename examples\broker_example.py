#!/usr/bin/env python3
"""
Example demonstrating ZerodhaBroker usage in mock mode.

This example shows how to:
1. Initialize the broker in mock mode
2. Connect and place orders
3. Check positions and account balance
4. Handle order cancellation

Run with: python examples/broker_example.py
"""

import sys
import os
from datetime import datetime

# Add framework to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from framework import ZerodhaBroker, Position, Order


def main():
    """Demonstrate broker functionality."""
    print("=== ZerodhaBroker Example ===\n")
    
    # Initialize broker in mock mode (no real API calls)
    print("1. Initializing ZerodhaBroker in mock mode...")
    broker = ZerodhaBroker(
        api_key="demo_key",
        api_secret="demo_secret", 
        access_token="demo_token",
        mock_orders=True
    )
    
    # Connect to broker
    print("2. Connecting to broker...")
    broker.connect()
    print("   ✓ Connected successfully\n")
    
    # Check initial account balance
    print("3. Checking account balance...")
    balance = broker.get_account_balance()
    print(f"   Cash: ₹{balance['cash']:,.2f}")
    print(f"   Margin Available: ₹{balance['margin_available']:,.2f}")
    print(f"   Total: ₹{balance['total']:,.2f}\n")
    
    # Place some orders
    print("4. Placing orders...")
    
    # Buy 100 shares of RELIANCE
    print("   Buying 100 RELIANCE...")
    order1 = broker.place_order("RELIANCE", 100, "market", 2500.0)
    print(f"   ✓ Order placed: {order1['order_id']}")
    
    # Buy 50 more shares of RELIANCE
    print("   Buying 50 more RELIANCE...")
    order2 = broker.place_order("RELIANCE", 50, "market", 2520.0)
    print(f"   ✓ Order placed: {order2['order_id']}")
    
    # Buy 25 shares of TCS
    print("   Buying 25 TCS...")
    order3 = broker.place_order("TCS", 25, "market", 3200.0)
    print(f"   ✓ Order placed: {order3['order_id']}")
    
    # Sell 30 shares of RELIANCE
    print("   Selling 30 RELIANCE...")
    order4 = broker.place_order("RELIANCE", -30, "market", 2530.0)
    print(f"   ✓ Order placed: {order4['order_id']}\n")
    
    # Check positions
    print("5. Checking positions...")
    positions = broker.get_positions()
    for position in positions:
        print(f"   {position.symbol}: {position.qty} shares @ ₹{position.avg_price:.2f} ({position.side})")
    print()
    
    # Demonstrate position calculation
    print("6. Position calculation explanation:")
    reliance_pos = next(p for p in positions if p.symbol == "RELIANCE")
    print(f"   RELIANCE position: {reliance_pos.qty} shares")
    print(f"   Calculation: 100 (buy) + 50 (buy) - 30 (sell) = 120 shares")
    print(f"   Average price calculation:")
    print(f"   - First buy: 100 @ ₹2500 = ₹250,000")
    print(f"   - Second buy: 50 @ ₹2520 = ₹126,000") 
    print(f"   - Total: ₹376,000 / 150 shares = ₹2506.67 avg")
    print(f"   - After selling 30 shares, avg remains ₹2506.67")
    print(f"   - Final position: 120 shares @ ₹{reliance_pos.avg_price:.2f}\n")
    
    # Cancel an order
    print("7. Cancelling an order...")
    cancel_result = broker.cancel_order(order1['order_id'])
    if cancel_result:
        print(f"   ✓ Order {order1['order_id']} cancelled successfully")
    else:
        print(f"   ✗ Failed to cancel order {order1['order_id']}")
    print()
    
    # Get option chain
    print("8. Getting option chain...")
    option_chain = broker.get_option_chain("NIFTY")
    print(f"   Option chain for {option_chain.symbol}:")
    for option in option_chain.chain[:3]:  # Show first 3 options
        print(f"   Strike {option['strike']}: Call ₹{option['call_price']}, Put ₹{option['put_price']}")
    print()
    
    # Demonstrate error handling
    print("9. Testing error handling...")
    broker.disconnect()
    try:
        broker.place_order("TEST", 1, "market")
    except RuntimeError as e:
        print(f"   ✓ Caught expected error: {e}")
    print()
    
    print("=== Example completed successfully! ===")


if __name__ == "__main__":
    main()
