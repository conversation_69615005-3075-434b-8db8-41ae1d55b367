"""
Market data handler for the algorithmic trading framework.

This module provides the Blotter class which handles real-time market data
including ticks, bars, quotes, and order book updates. It serves as the
central hub for all market data processing and distribution.
"""

import asyncio
import logging
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime


logger = logging.getLogger(__name__)


class Blotter:
    """
    Market data handler that processes and distributes real-time market data.
    
    The Blotter receives market data from various sources and distributes it
    to registered strategies and other components that need market data updates.
    """
    
    def __init__(self):
        """Initialize the Blotter with empty subscriber lists."""
        self.tick_subscribers: List[Callable] = []
        self.bar_subscribers: List[Callable] = []
        self.quote_subscribers: List[Callable] = []
        self.orderbook_subscribers: List[Callable] = []
        self.is_running = False
    
    def subscribe_tick(self, callback: Callable):
        """Subscribe to tick data updates."""
        self.tick_subscribers.append(callback)
    
    def subscribe_bar(self, callback: Callable):
        """Subscribe to bar data updates."""
        self.bar_subscribers.append(callback)
    
    def subscribe_quote(self, callback: Callable):
        """Subscribe to quote data updates."""
        self.quote_subscribers.append(callback)
    
    def subscribe_orderbook(self, callback: Callable):
        """Subscribe to order book updates."""
        self.orderbook_subscribers.append(callback)
    
    async def on_tick(self, symbol: str, price: float, volume: int, 
                     timestamp: datetime) -> None:
        """
        Handle incoming tick data.
        
        Args:
            symbol: Trading symbol
            price: Last traded price
            volume: Volume of the tick
            timestamp: Time of the tick
        """
        logger.debug(f"Received tick for {symbol}: price={price}, volume={volume}")
        
        tick_data = {
            'symbol': symbol,
            'price': price,
            'volume': volume,
            'timestamp': timestamp,
            'type': 'tick'
        }
        
        # Notify all tick subscribers
        for callback in self.tick_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(tick_data)
                else:
                    callback(tick_data)
            except Exception as e:
                logger.error(f"Error in tick callback: {e}")
    
    async def on_bar(self, symbol: str, open_price: float, high: float, 
                    low: float, close: float, volume: int, 
                    timestamp: datetime) -> None:
        """
        Handle incoming bar data.
        
        Args:
            symbol: Trading symbol
            open_price: Opening price of the bar
            high: Highest price in the bar
            low: Lowest price in the bar
            close: Closing price of the bar
            volume: Total volume in the bar
            timestamp: Time of the bar
        """
        logger.debug(f"Received bar for {symbol}: OHLCV=({open_price}, {high}, {low}, {close}, {volume})")
        
        bar_data = {
            'symbol': symbol,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume,
            'timestamp': timestamp,
            'type': 'bar'
        }
        
        # Notify all bar subscribers
        for callback in self.bar_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(bar_data)
                else:
                    callback(bar_data)
            except Exception as e:
                logger.error(f"Error in bar callback: {e}")
    
    async def on_quote(self, symbol: str, bid: float, ask: float, 
                      bid_size: int, ask_size: int, timestamp: datetime) -> None:
        """
        Handle incoming quote data.
        
        Args:
            symbol: Trading symbol
            bid: Best bid price
            ask: Best ask price
            bid_size: Size at best bid
            ask_size: Size at best ask
            timestamp: Time of the quote
        """
        logger.debug(f"Received quote for {symbol}: bid={bid}, ask={ask}")
        
        quote_data = {
            'symbol': symbol,
            'bid': bid,
            'ask': ask,
            'bid_size': bid_size,
            'ask_size': ask_size,
            'timestamp': timestamp,
            'type': 'quote'
        }
        
        # Notify all quote subscribers
        for callback in self.quote_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(quote_data)
                else:
                    callback(quote_data)
            except Exception as e:
                logger.error(f"Error in quote callback: {e}")
    
    async def on_orderbook(self, symbol: str, bids: List[Dict], 
                          asks: List[Dict], timestamp: datetime) -> None:
        """
        Handle incoming order book data.
        
        Args:
            symbol: Trading symbol
            bids: List of bid levels with price and size
            asks: List of ask levels with price and size
            timestamp: Time of the order book update
        """
        logger.debug(f"Received orderbook for {symbol}: {len(bids)} bids, {len(asks)} asks")
        
        orderbook_data = {
            'symbol': symbol,
            'bids': bids,
            'asks': asks,
            'timestamp': timestamp,
            'type': 'orderbook'
        }
        
        # Notify all orderbook subscribers
        for callback in self.orderbook_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(orderbook_data)
                else:
                    callback(orderbook_data)
            except Exception as e:
                logger.error(f"Error in orderbook callback: {e}")
    
    async def start(self):
        """Start the blotter for processing market data."""
        self.is_running = True
        logger.info("Blotter started")
    
    async def stop(self):
        """Stop the blotter."""
        self.is_running = False
        logger.info("Blotter stopped")
