"""
Setup script for the Algorithmic Trading Framework.

This script handles the installation and packaging of the framework
with all its dependencies and entry points.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
else:
    requirements = [
        'pandas>=1.5.0',
        'numpy>=1.21.0',
        'sqlalchemy>=1.4.0',
        'aiohttp>=3.8.0',
        'python-dotenv>=0.19.0',
        'ta-lib>=0.4.0',
        'pytest>=7.0.0',
        'pytest-asyncio>=0.21.0',
    ]

setup(
    name="algotrade-framework",
    version="0.1.0",
    author="Algotrade Framework Team",
    author_email="<EMAIL>",
    description="A comprehensive framework for developing and executing algorithmic trading strategies",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/algotrade-framework/algotrade-framework",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=0.991",
        ],
        "data": [
            "yfinance>=0.2.0",
            "alpha-vantage>=2.3.0",
        ],
        "database": [
            "psycopg2-binary>=2.9.0",
            "pymongo>=4.0.0",
        ],
        "web": [
            "fastapi>=0.95.0",
            "uvicorn>=0.20.0",
        ],
        "analytics": [
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "plotly>=5.0.0",
        ],
        "ml": [
            "scikit-learn>=1.1.0",
            "tensorflow>=2.10.0",
        ],
        "queue": [
            "redis>=4.0.0",
            "celery>=5.2.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "algotrade=cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "framework": ["py.typed"],
    },
    zip_safe=False,
    keywords="algorithmic trading, finance, trading strategies, backtesting, market data",
    project_urls={
        "Bug Reports": "https://github.com/algotrade-framework/algotrade-framework/issues",
        "Source": "https://github.com/algotrade-framework/algotrade-framework",
        "Documentation": "https://algotrade-framework.readthedocs.io/",
    },
)
