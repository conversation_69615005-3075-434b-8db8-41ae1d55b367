"""
Abstract broker interface for algorithmic trading framework.

This module defines the abstract base class for all broker implementations,
providing a standardized interface for connecting to different brokers,
placing orders, managing positions, and handling market data.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from .models import Position, Option<PERSON>hain, Tick


class Broker(ABC):
    """
    Abstract base class for broker implementations.

    All broker implementations must inherit from this class and implement
    the required abstract methods for connecting to the broker API,
    managing orders, retrieving account information, and handling market data.
    """

    @abstractmethod
    def connect(self) -> None:
        """
        Establish connection to the broker.

        Should set up REST API client and authenticate with broker.
        """
        pass

    @abstractmethod
    def disconnect(self) -> None:
        """
        Disconnect from the broker.

        Should clean up connections and release resources.
        """
        pass

    @abstractmethod
    def place_order(self, symbol: str, qty: int, order_type: str = "market",
                   price: Optional[float] = None, **kwargs) -> Dict[str, Any]:
        """
        Place an order with the broker.

        Args:
            symbol: Trading symbol (e.g., 'RELIANCE', 'NIFTY')
            qty: Order quantity (positive for buy, negative for sell)
            order_type: Type of order ('market', 'limit', 'stop', etc.)
            price: Price for limit orders (None for market orders)
            **kwargs: Additional broker-specific parameters

        Returns:
            Dict containing order response with status and order_id
        """
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """
        Cancel an existing order.

        Args:
            order_id: Unique identifier for the order to cancel

        Returns:
            True if cancellation successful, False otherwise
        """
        pass

    @abstractmethod
    def get_positions(self) -> List[Position]:
        """
        Retrieve current positions from the broker.

        Returns:
            List of Position objects representing current holdings
        """
        pass

    @abstractmethod
    def get_account_balance(self) -> Dict[str, Any]:
        """
        Retrieve account balance and margin information.

        Returns:
            Dictionary containing balance details (cash, margin, etc.)
        """
        pass

    @abstractmethod
    def get_option_chain(self, symbol: str) -> OptionChain:
        """
        Retrieve option chain data for a given underlying symbol.

        Args:
            symbol: Underlying symbol (e.g., 'NIFTY', 'BANKNIFTY')

        Returns:
            OptionChain object containing option contracts
        """
        pass

    @abstractmethod
    def start_market_ticker(self, symbols: List[str], on_tick: Callable[[Tick], None]) -> None:
        """
        Start real-time market data feed for given symbols.

        Opens websocket connection and calls on_tick callback for each tick.

        Args:
            symbols: List of symbols to subscribe to
            on_tick: Callback function to handle incoming tick data
        """
        pass
