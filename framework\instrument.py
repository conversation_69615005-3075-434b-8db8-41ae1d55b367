"""
Data model for financial instruments in the algorithmic trading framework.

This module defines the Instrument class which represents tradeable financial
instruments with their properties and metadata.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from decimal import Decimal
from enum import Enum


class InstrumentType(Enum):
    """Enumeration of supported instrument types."""
    EQUITY = "equity"
    FUTURES = "futures"
    OPTIONS = "options"
    FOREX = "forex"
    CRYPTO = "crypto"
    BOND = "bond"
    COMMODITY = "commodity"


class Exchange(Enum):
    """Enumeration of supported exchanges."""
    NSE = "NSE"
    BSE = "BSE"
    MCX = "MCX"
    NCDEX = "NCDEX"
    NASDAQ = "NASDAQ"
    NYSE = "NYSE"
    BINANCE = "BINANCE"
    OTHER = "OTHER"


@dataclass
class Instrument:
    """
    Data model representing a tradeable financial instrument.
    
    This class encapsulates all the metadata and properties of a financial
    instrument including symbol, exchange, type, and trading specifications.
    """
    
    symbol: str
    exchange: Exchange
    instrument_type: InstrumentType
    name: Optional[str] = None
    isin: Optional[str] = None
    lot_size: int = 1
    tick_size: Decimal = Decimal('0.01')
    expiry_date: Optional[str] = None
    strike_price: Optional[Decimal] = None
    option_type: Optional[str] = None  # 'CE' or 'PE' for options
    underlying: Optional[str] = None
    currency: str = "INR"
    margin_required: Optional[Decimal] = None
    is_tradeable: bool = True
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        if self.metadata is None:
            self.metadata = {}
        
        # Validate option-specific fields
        if self.instrument_type == InstrumentType.OPTIONS:
            if self.strike_price is None:
                raise ValueError("Options must have a strike price")
            if self.option_type not in ['CE', 'PE']:
                raise ValueError("Option type must be 'CE' or 'PE'")
            if self.expiry_date is None:
                raise ValueError("Options must have an expiry date")
    
    @property
    def full_symbol(self) -> str:
        """
        Get the full symbol including exchange prefix.
        
        Returns:
            Full symbol string with exchange prefix
        """
        return f"{self.exchange.value}:{self.symbol}"
    
    @property
    def is_derivative(self) -> bool:
        """
        Check if the instrument is a derivative.
        
        Returns:
            True if the instrument is a derivative (futures/options)
        """
        return self.instrument_type in [InstrumentType.FUTURES, InstrumentType.OPTIONS]
    
    @property
    def is_option(self) -> bool:
        """
        Check if the instrument is an option.
        
        Returns:
            True if the instrument is an option
        """
        return self.instrument_type == InstrumentType.OPTIONS
    
    @property
    def is_future(self) -> bool:
        """
        Check if the instrument is a future.
        
        Returns:
            True if the instrument is a future
        """
        return self.instrument_type == InstrumentType.FUTURES
    
    def get_contract_value(self, price: Decimal) -> Decimal:
        """
        Calculate the contract value for the given price.
        
        Args:
            price: Price per unit
            
        Returns:
            Total contract value (price * lot_size)
        """
        return price * self.lot_size
    
    def round_to_tick_size(self, price: Decimal) -> Decimal:
        """
        Round price to the nearest valid tick size.
        
        Args:
            price: Price to round
            
        Returns:
            Price rounded to nearest tick size
        """
        if self.tick_size == 0:
            return price
        
        return (price / self.tick_size).quantize(Decimal('1')) * self.tick_size
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert instrument to dictionary representation.
        
        Returns:
            Dictionary containing all instrument data
        """
        return {
            'symbol': self.symbol,
            'exchange': self.exchange.value,
            'instrument_type': self.instrument_type.value,
            'name': self.name,
            'isin': self.isin,
            'lot_size': self.lot_size,
            'tick_size': str(self.tick_size),
            'expiry_date': self.expiry_date,
            'strike_price': str(self.strike_price) if self.strike_price else None,
            'option_type': self.option_type,
            'underlying': self.underlying,
            'currency': self.currency,
            'margin_required': str(self.margin_required) if self.margin_required else None,
            'is_tradeable': self.is_tradeable,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Instrument':
        """
        Create instrument from dictionary representation.
        
        Args:
            data: Dictionary containing instrument data
            
        Returns:
            Instrument instance
        """
        return cls(
            symbol=data['symbol'],
            exchange=Exchange(data['exchange']),
            instrument_type=InstrumentType(data['instrument_type']),
            name=data.get('name'),
            isin=data.get('isin'),
            lot_size=data.get('lot_size', 1),
            tick_size=Decimal(data.get('tick_size', '0.01')),
            expiry_date=data.get('expiry_date'),
            strike_price=Decimal(data['strike_price']) if data.get('strike_price') else None,
            option_type=data.get('option_type'),
            underlying=data.get('underlying'),
            currency=data.get('currency', 'INR'),
            margin_required=Decimal(data['margin_required']) if data.get('margin_required') else None,
            is_tradeable=data.get('is_tradeable', True),
            metadata=data.get('metadata', {})
        )
    
    def __str__(self) -> str:
        """String representation of the instrument."""
        return f"{self.full_symbol} ({self.instrument_type.value})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the instrument."""
        return f"Instrument(symbol='{self.symbol}', exchange={self.exchange}, type={self.instrument_type})"
