"""
Unit tests for the Blotter market data handler.

This module contains tests for the Blotter class which handles
real-time market data processing and distribution.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from framework.blotter import Blotter


class TestBlotter:
    """Test cases for the Blotter class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.blotter = Blotter()
        self.test_timestamp = datetime.now()
    
    def test_blotter_initialization(self):
        """Test Blotter initialization."""
        assert self.blotter.tick_subscribers == []
        assert self.blotter.bar_subscribers == []
        assert self.blotter.quote_subscribers == []
        assert self.blotter.orderbook_subscribers == []
        assert self.blotter.is_running is False
    
    def test_subscribe_tick(self):
        """Test subscribing to tick data."""
        callback = Mock()
        self.blotter.subscribe_tick(callback)
        
        assert callback in self.blotter.tick_subscribers
    
    def test_subscribe_bar(self):
        """Test subscribing to bar data."""
        callback = Mock()
        self.blotter.subscribe_bar(callback)
        
        assert callback in self.blotter.bar_subscribers
    
    def test_subscribe_quote(self):
        """Test subscribing to quote data."""
        callback = Mock()
        self.blotter.subscribe_quote(callback)
        
        assert callback in self.blotter.quote_subscribers
    
    def test_subscribe_orderbook(self):
        """Test subscribing to orderbook data."""
        callback = Mock()
        self.blotter.subscribe_orderbook(callback)
        
        assert callback in self.blotter.orderbook_subscribers
    
    @pytest.mark.asyncio
    async def test_on_tick(self):
        """Test tick data handling."""
        # Setup mock callback
        callback = Mock()
        self.blotter.subscribe_tick(callback)
        
        # Send tick data
        await self.blotter.on_tick(
            symbol='RELIANCE',
            price=2500.50,
            volume=1000,
            timestamp=self.test_timestamp
        )
        
        # Verify callback was called
        callback.assert_called_once()
        call_args = callback.call_args[0][0]
        
        assert call_args['symbol'] == 'RELIANCE'
        assert call_args['price'] == 2500.50
        assert call_args['volume'] == 1000
        assert call_args['timestamp'] == self.test_timestamp
        assert call_args['type'] == 'tick'
    
    @pytest.mark.asyncio
    async def test_on_tick_async_callback(self):
        """Test tick data handling with async callback."""
        # Setup async mock callback
        callback = AsyncMock()
        self.blotter.subscribe_tick(callback)
        
        # Send tick data
        await self.blotter.on_tick(
            symbol='TCS',
            price=3200.25,
            volume=500,
            timestamp=self.test_timestamp
        )
        
        # Verify async callback was called
        callback.assert_called_once()
        call_args = callback.call_args[0][0]
        assert call_args['symbol'] == 'TCS'
    
    @pytest.mark.asyncio
    async def test_on_bar(self):
        """Test bar data handling."""
        # Setup mock callback
        callback = Mock()
        self.blotter.subscribe_bar(callback)
        
        # Send bar data
        await self.blotter.on_bar(
            symbol='NIFTY',
            open_price=18000.0,
            high=18100.0,
            low=17950.0,
            close=18050.0,
            volume=1000000,
            timestamp=self.test_timestamp
        )
        
        # Verify callback was called
        callback.assert_called_once()
        call_args = callback.call_args[0][0]
        
        assert call_args['symbol'] == 'NIFTY'
        assert call_args['open'] == 18000.0
        assert call_args['high'] == 18100.0
        assert call_args['low'] == 17950.0
        assert call_args['close'] == 18050.0
        assert call_args['volume'] == 1000000
        assert call_args['type'] == 'bar'
    
    @pytest.mark.asyncio
    async def test_on_quote(self):
        """Test quote data handling."""
        # Setup mock callback
        callback = Mock()
        self.blotter.subscribe_quote(callback)
        
        # Send quote data
        await self.blotter.on_quote(
            symbol='BANKNIFTY',
            bid=42500.0,
            ask=42505.0,
            bid_size=100,
            ask_size=150,
            timestamp=self.test_timestamp
        )
        
        # Verify callback was called
        callback.assert_called_once()
        call_args = callback.call_args[0][0]
        
        assert call_args['symbol'] == 'BANKNIFTY'
        assert call_args['bid'] == 42500.0
        assert call_args['ask'] == 42505.0
        assert call_args['bid_size'] == 100
        assert call_args['ask_size'] == 150
        assert call_args['type'] == 'quote'
    
    @pytest.mark.asyncio
    async def test_on_orderbook(self):
        """Test orderbook data handling."""
        # Setup mock callback
        callback = Mock()
        self.blotter.subscribe_orderbook(callback)
        
        # Sample orderbook data
        bids = [
            {'price': 2500.0, 'size': 100},
            {'price': 2499.5, 'size': 200}
        ]
        asks = [
            {'price': 2500.5, 'size': 150},
            {'price': 2501.0, 'size': 250}
        ]
        
        # Send orderbook data
        await self.blotter.on_orderbook(
            symbol='RELIANCE',
            bids=bids,
            asks=asks,
            timestamp=self.test_timestamp
        )
        
        # Verify callback was called
        callback.assert_called_once()
        call_args = callback.call_args[0][0]
        
        assert call_args['symbol'] == 'RELIANCE'
        assert call_args['bids'] == bids
        assert call_args['asks'] == asks
        assert call_args['type'] == 'orderbook'
    
    @pytest.mark.asyncio
    async def test_multiple_subscribers(self):
        """Test multiple subscribers for the same data type."""
        # Setup multiple callbacks
        callback1 = Mock()
        callback2 = Mock()
        callback3 = Mock()
        
        self.blotter.subscribe_tick(callback1)
        self.blotter.subscribe_tick(callback2)
        self.blotter.subscribe_tick(callback3)
        
        # Send tick data
        await self.blotter.on_tick(
            symbol='INFY',
            price=1500.0,
            volume=2000,
            timestamp=self.test_timestamp
        )
        
        # Verify all callbacks were called
        callback1.assert_called_once()
        callback2.assert_called_once()
        callback3.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_callback_exception_handling(self):
        """Test that exceptions in callbacks don't break the blotter."""
        # Setup callback that raises exception
        def failing_callback(data):
            raise ValueError("Test exception")
        
        # Setup normal callback
        normal_callback = Mock()
        
        self.blotter.subscribe_tick(failing_callback)
        self.blotter.subscribe_tick(normal_callback)
        
        # Send tick data - should not raise exception
        await self.blotter.on_tick(
            symbol='HDFC',
            price=1600.0,
            volume=1500,
            timestamp=self.test_timestamp
        )
        
        # Normal callback should still be called
        normal_callback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_stop(self):
        """Test starting and stopping the blotter."""
        assert self.blotter.is_running is False
        
        # Start blotter
        await self.blotter.start()
        assert self.blotter.is_running is True
        
        # Stop blotter
        await self.blotter.stop()
        assert self.blotter.is_running is False
    
    @pytest.mark.asyncio
    async def test_mixed_sync_async_callbacks(self):
        """Test handling of both sync and async callbacks."""
        # Setup mixed callbacks
        sync_callback = Mock()
        async_callback = AsyncMock()
        
        self.blotter.subscribe_bar(sync_callback)
        self.blotter.subscribe_bar(async_callback)
        
        # Send bar data
        await self.blotter.on_bar(
            symbol='SBIN',
            open_price=500.0,
            high=505.0,
            low=495.0,
            close=502.0,
            volume=50000,
            timestamp=self.test_timestamp
        )
        
        # Verify both callbacks were called
        sync_callback.assert_called_once()
        async_callback.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__])
