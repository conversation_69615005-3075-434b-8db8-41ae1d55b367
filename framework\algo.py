"""
Abstract algorithm base class for the algorithmic trading framework.

This module defines the abstract base class for all trading algorithms,
providing lifecycle hooks and a standardized interface for strategy development.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
import logging


logger = logging.getLogger(__name__)


class Algo(ABC):
    """
    Abstract base class for trading algorithms.
    
    All trading strategies must inherit from this class and implement
    the required lifecycle hooks for handling market data, order fills,
    and algorithm lifecycle events.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the algorithm.
        
        Args:
            name: Name of the algorithm
            config: Optional configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.is_running = False
        self.positions = {}
        self.orders = {}
        
        logger.info(f"Initialized algorithm: {self.name}")
    
    @abstractmethod
    async def on_start(self) -> None:
        """
        Called when the algorithm starts.
        
        This method should contain initialization logic such as:
        - Setting up indicators
        - Loading historical data
        - Initializing state variables
        """
        pass
    
    @abstractmethod
    async def on_stop(self) -> None:
        """
        Called when the algorithm stops.
        
        This method should contain cleanup logic such as:
        - Closing positions
        - Saving state
        - Cleanup resources
        """
        pass
    
    @abstractmethod
    async def on_tick(self, tick_data: Dict[str, Any]) -> None:
        """
        Called on each tick update.
        
        Args:
            tick_data: Dictionary containing tick information
                - symbol: Trading symbol
                - price: Last traded price
                - volume: Volume of the tick
                - timestamp: Time of the tick
        """
        pass
    
    @abstractmethod
    async def on_bar(self, bar_data: Dict[str, Any]) -> None:
        """
        Called on each bar update.
        
        Args:
            bar_data: Dictionary containing bar information
                - symbol: Trading symbol
                - open: Opening price
                - high: Highest price
                - low: Lowest price
                - close: Closing price
                - volume: Total volume
                - timestamp: Time of the bar
        """
        pass
    
    @abstractmethod
    async def on_fill(self, fill_data: Dict[str, Any]) -> None:
        """
        Called when an order is filled.
        
        Args:
            fill_data: Dictionary containing fill information
                - order_id: Order identifier
                - symbol: Trading symbol
                - quantity: Filled quantity
                - price: Fill price
                - timestamp: Time of the fill
        """
        pass
    
    @abstractmethod
    async def on_quote(self, quote_data: Dict[str, Any]) -> None:
        """
        Called on each quote update.
        
        Args:
            quote_data: Dictionary containing quote information
                - symbol: Trading symbol
                - bid: Best bid price
                - ask: Best ask price
                - bid_size: Size at best bid
                - ask_size: Size at best ask
                - timestamp: Time of the quote
        """
        pass
    
    @abstractmethod
    async def on_orderbook(self, orderbook_data: Dict[str, Any]) -> None:
        """
        Called on each order book update.
        
        Args:
            orderbook_data: Dictionary containing order book information
                - symbol: Trading symbol
                - bids: List of bid levels
                - asks: List of ask levels
                - timestamp: Time of the update
        """
        pass
    
    async def start(self) -> None:
        """Start the algorithm."""
        if self.is_running:
            logger.warning(f"Algorithm {self.name} is already running")
            return
        
        logger.info(f"Starting algorithm: {self.name}")
        self.is_running = True
        await self.on_start()
        logger.info(f"Algorithm {self.name} started successfully")
    
    async def stop(self) -> None:
        """Stop the algorithm."""
        if not self.is_running:
            logger.warning(f"Algorithm {self.name} is not running")
            return
        
        logger.info(f"Stopping algorithm: {self.name}")
        self.is_running = False
        await self.on_stop()
        logger.info(f"Algorithm {self.name} stopped successfully")
    
    def get_position(self, symbol: str) -> int:
        """
        Get current position for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current position quantity (positive for long, negative for short)
        """
        return self.positions.get(symbol, 0)
    
    def update_position(self, symbol: str, quantity: int) -> None:
        """
        Update position for a symbol.
        
        Args:
            symbol: Trading symbol
            quantity: Position change (positive for buy, negative for sell)
        """
        current_position = self.positions.get(symbol, 0)
        self.positions[symbol] = current_position + quantity
        logger.debug(f"Updated position for {symbol}: {self.positions[symbol]}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        return self.config.get(key, default)
