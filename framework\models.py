"""
Data models for the algorithmic trading framework.

This module defines dataclasses for trading entities like positions, orders,
instruments, market data, and option chains used throughout the framework.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any, List
import uuid


@dataclass
class Position:
    """
    Represents a trading position for a specific symbol.
    
    Tracks quantity, average price, side (long/short), and last update time.
    """
    symbol: str  # Trading symbol (e.g., "RELIANCE", "NIFTY")
    qty: int  # Current quantity held (positive for long, negative for short)
    avg_price: float  # Average price of the position
    side: str  # Position side: "long" or "short"
    updated_at: datetime  # Last time position was updated
    
    def update_on_fill(self, qty: int, fill_price: float, side: str) -> None:
        """
        Update position when an order is filled.
        
        Args:
            qty: Quantity filled (positive number)
            fill_price: Price at which order was filled
            side: Side of the fill ("buy" or "sell")
        """
        if side == "buy":
            # For buy orders, add to position
            if self.qty >= 0:  # Currently long or flat
                # Calculate new average: (old_avg*old_qty + fill_price*fill_qty)/(old_qty + fill_qty)
                new_total_value = (self.avg_price * self.qty) + (fill_price * qty)
                new_total_qty = self.qty + qty
                self.avg_price = new_total_value / new_total_qty if new_total_qty != 0 else fill_price
                self.qty = new_total_qty
                self.side = "long"
            else:  # Currently short
                if qty > abs(self.qty):  # Fill is larger than short position
                    remaining_qty = qty - abs(self.qty)
                    self.qty = remaining_qty
                    self.avg_price = fill_price
                    self.side = "long"
                else:  # Fill reduces short position
                    self.qty += qty  # qty is positive, so this reduces the negative position
                    if self.qty == 0:
                        self.side = "flat"
        
        elif side == "sell":
            # For sell orders, subtract from position
            if self.qty <= 0:  # Currently short or flat
                # Calculate new average for short position
                new_total_value = (self.avg_price * abs(self.qty)) + (fill_price * qty)
                new_total_qty = abs(self.qty) + qty
                self.avg_price = new_total_value / new_total_qty if new_total_qty != 0 else fill_price
                self.qty = -new_total_qty
                self.side = "short"
            else:  # Currently long
                if qty > self.qty:  # Sell is larger than long position
                    remaining_qty = qty - self.qty
                    self.qty = -remaining_qty
                    self.avg_price = fill_price
                    self.side = "short"
                else:  # Sell reduces long position
                    self.qty -= qty
                    if self.qty == 0:
                        self.side = "flat"
        
        self.updated_at = datetime.now()


@dataclass
class Order:
    """
    Represents a trading order with all relevant details.
    
    Tracks order ID, symbol, quantity, type, status, price, and timestamps.
    """
    order_id: str  # Unique order identifier
    symbol: str  # Trading symbol
    qty: int  # Order quantity (positive for buy, negative for sell)
    order_type: str  # Order type: "market", "limit", "stop", etc.
    status: str  # Order status: "pending", "open", "filled", "cancelled"
    price: Optional[float] = None  # Order price (None for market orders)
    created_at: datetime = field(default_factory=datetime.now)  # Order creation time
    filled_at: Optional[datetime] = None  # Order fill time (None if not filled)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert order to dictionary representation.
        
        Returns:
            Dictionary containing all order data
        """
        return {
            "order_id": self.order_id,
            "symbol": self.symbol,
            "qty": self.qty,
            "order_type": self.order_type,
            "status": self.status,
            "price": self.price,
            "created_at": self.created_at.isoformat(),
            "filled_at": self.filled_at.isoformat() if self.filled_at else None
        }


@dataclass
class OptionChain:
    """
    Represents option chain data for a given underlying symbol.
    
    Contains the underlying symbol and list of option contracts.
    """
    symbol: str  # Underlying symbol (e.g., "NIFTY", "BANKNIFTY")
    chain: List[Dict[str, Any]]  # List of option contract dictionaries


@dataclass
class Instrument:
    """
    Represents a tradeable financial instrument.
    
    Contains basic instrument information like symbol, exchange, and lot size.
    """
    symbol: str  # Trading symbol
    exchange: str  # Exchange name (e.g., "NSE", "BSE")
    lot_size: Optional[int] = None  # Lot size for derivatives (None for equities)


@dataclass
class OHLC:
    """
    Represents OHLC (Open, High, Low, Close) bar data.
    
    Contains price and volume data for a specific time period.
    """
    ticker_id: str  # Unique ticker identifier
    symbol: str  # Trading symbol
    datetime: datetime  # Bar timestamp
    open: float  # Opening price
    high: float  # Highest price
    low: float  # Lowest price
    close: float  # Closing price
    volume: int  # Trading volume
    vwap: Optional[float] = None  # Volume weighted average price
    resolution: str = "1min"  # Bar resolution (e.g., "1min", "5min", "1day")


@dataclass
class Tick:
    """
    Represents real-time tick data from market feed.
    
    Contains last price, bid/ask, and volume information.
    """
    symbol: str  # Trading symbol
    timestamp: datetime  # Tick timestamp
    last_price: float  # Last traded price
    bid: Optional[float] = None  # Best bid price
    ask: Optional[float] = None  # Best ask price
    volume: Optional[int] = None  # Tick volume
