"""
Moving Average Crossover strategy implementation.

This module contains a simple moving average crossover strategy that
demonstrates how to implement trading algorithms using the framework.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..algo import Algo
from ..indicators import SimpleMovingAverage, ExponentialMovingAverage


logger = logging.getLogger(__name__)


class MovingAverageCrossover(Algo):
    """
    Moving Average Crossover trading strategy.
    
    This strategy generates buy signals when a fast moving average crosses
    above a slow moving average, and sell signals when the fast MA crosses
    below the slow MA.
    """
    
    def __init__(self, name: str = "MA_Crossover", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Moving Average Crossover strategy.
        
        Args:
            name: Name of the strategy
            config: Configuration dictionary with the following keys:
                   - fast_period: Period for fast moving average (default: 10)
                   - slow_period: Period for slow moving average (default: 20)
                   - ma_type: Type of moving average ('SMA' or 'EMA', default: 'SMA')
                   - symbol: Trading symbol to trade
                   - quantity: Quantity to trade per signal
        """
        super().__init__(name, config)
        
        # Strategy parameters
        self.fast_period = self.get_config('fast_period', 10)
        self.slow_period = self.get_config('slow_period', 20)
        self.ma_type = self.get_config('ma_type', 'SMA')
        self.symbol = self.get_config('symbol', 'RELIANCE')
        self.quantity = self.get_config('quantity', 100)
        
        # Initialize moving averages
        if self.ma_type == 'EMA':
            self.fast_ma = ExponentialMovingAverage(self.fast_period)
            self.slow_ma = ExponentialMovingAverage(self.slow_period)
        else:
            self.fast_ma = SimpleMovingAverage(self.fast_period)
            self.slow_ma = SimpleMovingAverage(self.slow_period)
        
        # Strategy state
        self.last_fast_ma = None
        self.last_slow_ma = None
        self.current_fast_ma = None
        self.current_slow_ma = None
        self.last_signal = None
        
        logger.info(f"Initialized {self.name} strategy: {self.ma_type}({self.fast_period}, {self.slow_period})")
    
    async def on_start(self) -> None:
        """Called when the strategy starts."""
        logger.info(f"Starting {self.name} strategy for symbol: {self.symbol}")
        
        # Initialize any required state or data
        self.last_signal = None
        
        logger.info(f"{self.name} strategy started successfully")
    
    async def on_stop(self) -> None:
        """Called when the strategy stops."""
        logger.info(f"Stopping {self.name} strategy")
        
        # Close any open positions
        current_position = self.get_position(self.symbol)
        if current_position != 0:
            logger.info(f"Closing position: {current_position} shares of {self.symbol}")
            # TODO: Place closing order through broker
        
        logger.info(f"{self.name} strategy stopped successfully")
    
    async def on_tick(self, tick_data: Dict[str, Any]) -> None:
        """
        Called on each tick update.
        
        Args:
            tick_data: Dictionary containing tick information
        """
        symbol = tick_data.get('symbol')
        if symbol != self.symbol:
            return
        
        price = tick_data.get('price')
        if price is None:
            return
        
        # Update moving averages
        self._update_moving_averages(price)
        
        # Check for crossover signals
        await self._check_signals()
    
    async def on_bar(self, bar_data: Dict[str, Any]) -> None:
        """
        Called on each bar update.
        
        Args:
            bar_data: Dictionary containing bar information
        """
        symbol = bar_data.get('symbol')
        if symbol != self.symbol:
            return
        
        close_price = bar_data.get('close')
        if close_price is None:
            return
        
        # Update moving averages with close price
        self._update_moving_averages(close_price)
        
        # Check for crossover signals
        await self._check_signals()
        
        # Log current state
        if self.current_fast_ma and self.current_slow_ma:
            logger.debug(f"{self.symbol}: Fast MA={self.current_fast_ma:.2f}, Slow MA={self.current_slow_ma:.2f}")
    
    async def on_fill(self, fill_data: Dict[str, Any]) -> None:
        """
        Called when an order is filled.
        
        Args:
            fill_data: Dictionary containing fill information
        """
        symbol = fill_data.get('symbol')
        if symbol != self.symbol:
            return
        
        quantity = fill_data.get('quantity', 0)
        price = fill_data.get('price', 0)
        
        # Update position
        self.update_position(symbol, quantity)
        
        logger.info(f"Order filled: {quantity} shares of {symbol} at {price}")
        logger.info(f"Current position: {self.get_position(symbol)} shares")
    
    async def on_quote(self, quote_data: Dict[str, Any]) -> None:
        """
        Called on each quote update.
        
        Args:
            quote_data: Dictionary containing quote information
        """
        # This strategy doesn't use quote data
        pass
    
    async def on_orderbook(self, orderbook_data: Dict[str, Any]) -> None:
        """
        Called on each order book update.
        
        Args:
            orderbook_data: Dictionary containing order book information
        """
        # This strategy doesn't use order book data
        pass
    
    def _update_moving_averages(self, price: float) -> None:
        """
        Update moving averages with new price.
        
        Args:
            price: New price value
        """
        # Store previous values
        self.last_fast_ma = self.current_fast_ma
        self.last_slow_ma = self.current_slow_ma
        
        # Update with new price
        self.current_fast_ma = self.fast_ma.update(price)
        self.current_slow_ma = self.slow_ma.update(price)
    
    async def _check_signals(self) -> None:
        """Check for crossover signals and generate trades."""
        if not all([self.current_fast_ma, self.current_slow_ma, 
                   self.last_fast_ma, self.last_slow_ma]):
            return
        
        current_position = self.get_position(self.symbol)
        
        # Check for bullish crossover (fast MA crosses above slow MA)
        if (self.last_fast_ma <= self.last_slow_ma and 
            self.current_fast_ma > self.current_slow_ma):
            
            if current_position <= 0:  # Not long or short
                await self._generate_buy_signal()
        
        # Check for bearish crossover (fast MA crosses below slow MA)
        elif (self.last_fast_ma >= self.last_slow_ma and 
              self.current_fast_ma < self.current_slow_ma):
            
            if current_position >= 0:  # Not short or long
                await self._generate_sell_signal()
    
    async def _generate_buy_signal(self) -> None:
        """Generate a buy signal."""
        logger.info(f"BUY SIGNAL: Fast MA crossed above Slow MA for {self.symbol}")
        logger.info(f"Fast MA: {self.current_fast_ma:.2f}, Slow MA: {self.current_slow_ma:.2f}")
        
        # TODO: Place buy order through broker
        # order = await self.broker.place_order(
        #     symbol=self.symbol,
        #     quantity=self.quantity,
        #     order_type='MARKET'
        # )
        
        self.last_signal = 'BUY'
        logger.info(f"Buy order placed for {self.quantity} shares of {self.symbol}")
    
    async def _generate_sell_signal(self) -> None:
        """Generate a sell signal."""
        logger.info(f"SELL SIGNAL: Fast MA crossed below Slow MA for {self.symbol}")
        logger.info(f"Fast MA: {self.current_fast_ma:.2f}, Slow MA: {self.current_slow_ma:.2f}")
        
        # TODO: Place sell order through broker
        # order = await self.broker.place_order(
        #     symbol=self.symbol,
        #     quantity=-self.quantity,
        #     order_type='MARKET'
        # )
        
        self.last_signal = 'SELL'
        logger.info(f"Sell order placed for {self.quantity} shares of {self.symbol}")
    
    def get_strategy_state(self) -> Dict[str, Any]:
        """
        Get current strategy state.
        
        Returns:
            Dictionary containing current strategy state
        """
        return {
            'symbol': self.symbol,
            'fast_ma': self.current_fast_ma,
            'slow_ma': self.current_slow_ma,
            'last_signal': self.last_signal,
            'position': self.get_position(self.symbol),
            'is_running': self.is_running
        }
