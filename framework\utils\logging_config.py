"""
Logging configuration for the algorithmic trading framework.

This module provides centralized logging configuration with support for
different log levels, formatters, and output destinations.
"""

import logging
import logging.handlers
import sys
from typing import Optional, Dict, Any
from pathlib import Path
import os


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: Optional[str] = None,
    date_format: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True
) -> None:
    """
    Setup logging configuration for the application.
    
    Args:
        level: Logging level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        log_file: Path to log file (optional, logs to console if not provided)
        log_format: Custom log format string (optional)
        date_format: Custom date format string (optional)
        max_bytes: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        console_output: Whether to output logs to console
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Default log format
    if log_format is None:
        log_format = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(funcName)s() - %(message)s"
        )
    
    # Default date format
    if date_format is None:
        date_format = "%Y-%m-%d %H:%M:%S"
    
    # Create formatter
    formatter = logging.Formatter(log_format, date_format)
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Create log directory if it doesn't exist
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Set specific logger levels for third-party libraries
    _configure_third_party_loggers()
    
    logging.info(f"Logging configured: level={level}, file={log_file}")


def _configure_third_party_loggers() -> None:
    """Configure logging levels for third-party libraries."""
    # Reduce noise from third-party libraries
    third_party_loggers = {
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'aiohttp': logging.WARNING,
        'websockets': logging.WARNING,
        'asyncio': logging.WARNING,
        'matplotlib': logging.WARNING,
        'pandas': logging.WARNING,
        'numpy': logging.WARNING,
    }
    
    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def setup_strategy_logging(strategy_name: str, log_dir: str = "logs") -> logging.Logger:
    """
    Setup dedicated logging for a specific strategy.
    
    Args:
        strategy_name: Name of the strategy
        log_dir: Directory to store log files
        
    Returns:
        Logger instance for the strategy
    """
    # Create strategy-specific logger
    logger_name = f"strategy.{strategy_name}"
    strategy_logger = logging.getLogger(logger_name)
    
    # Prevent duplicate handlers
    if strategy_logger.handlers:
        return strategy_logger
    
    # Create log directory
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Strategy log file
    log_file = log_path / f"{strategy_name}.log"
    
    # Create file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3
    )
    
    # Strategy-specific format
    formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(message)s",
        "%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(formatter)
    
    strategy_logger.addHandler(file_handler)
    strategy_logger.setLevel(logging.INFO)
    
    # Don't propagate to root logger to avoid duplicate messages
    strategy_logger.propagate = False
    
    return strategy_logger


def setup_broker_logging(broker_name: str, log_dir: str = "logs") -> logging.Logger:
    """
    Setup dedicated logging for a specific broker.
    
    Args:
        broker_name: Name of the broker
        log_dir: Directory to store log files
        
    Returns:
        Logger instance for the broker
    """
    # Create broker-specific logger
    logger_name = f"broker.{broker_name}"
    broker_logger = logging.getLogger(logger_name)
    
    # Prevent duplicate handlers
    if broker_logger.handlers:
        return broker_logger
    
    # Create log directory
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Broker log file
    log_file = log_path / f"{broker_name}.log"
    
    # Create file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    
    # Broker-specific format
    formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(funcName)s() - %(message)s",
        "%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(formatter)
    
    broker_logger.addHandler(file_handler)
    broker_logger.setLevel(logging.DEBUG)
    
    # Don't propagate to root logger to avoid duplicate messages
    broker_logger.propagate = False
    
    return broker_logger


def configure_logging_from_config(config: Dict[str, Any]) -> None:
    """
    Configure logging from configuration dictionary.
    
    Args:
        config: Configuration dictionary with logging settings
    """
    logging_config = config.get('logging', {})
    
    setup_logging(
        level=logging_config.get('level', 'INFO'),
        log_file=logging_config.get('file'),
        log_format=logging_config.get('format'),
        date_format=logging_config.get('date_format'),
        max_bytes=logging_config.get('max_bytes', 10 * 1024 * 1024),
        backup_count=logging_config.get('backup_count', 5),
        console_output=logging_config.get('console_output', True)
    )


class LoggerMixin:
    """
    Mixin class to add logging capability to any class.
    
    This mixin provides a logger property that returns a logger
    instance named after the class.
    """
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")


# Predefined logging configurations
LOGGING_CONFIGS = {
    'development': {
        'level': 'DEBUG',
        'console_output': True,
        'file': 'logs/algotrade_dev.log'
    },
    'production': {
        'level': 'INFO',
        'console_output': False,
        'file': 'logs/algotrade_prod.log'
    },
    'testing': {
        'level': 'WARNING',
        'console_output': True,
        'file': None
    }
}


def setup_environment_logging(environment: str = 'development') -> None:
    """
    Setup logging for specific environment.
    
    Args:
        environment: Environment name ('development', 'production', 'testing')
    """
    if environment not in LOGGING_CONFIGS:
        raise ValueError(f"Unknown environment: {environment}")
    
    config = LOGGING_CONFIGS[environment]
    setup_logging(**config)
    
    logging.info(f"Logging configured for {environment} environment")
