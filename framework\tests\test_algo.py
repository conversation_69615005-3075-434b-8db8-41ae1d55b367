"""
Unit tests for the Algo abstract base class.

This module contains tests for the abstract Algo class and its
lifecycle management functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from framework.algo import Algo


class TestAlgoImplementation(Algo):
    """Concrete implementation of Algo for testing."""
    
    def __init__(self, name="TestAlgo", config=None):
        super().__init__(name, config)
        self.start_called = False
        self.stop_called = False
        self.tick_data_received = []
        self.bar_data_received = []
        self.fill_data_received = []
        self.quote_data_received = []
        self.orderbook_data_received = []
    
    async def on_start(self):
        self.start_called = True
    
    async def on_stop(self):
        self.stop_called = True
    
    async def on_tick(self, tick_data):
        self.tick_data_received.append(tick_data)
    
    async def on_bar(self, bar_data):
        self.bar_data_received.append(bar_data)
    
    async def on_fill(self, fill_data):
        self.fill_data_received.append(fill_data)
    
    async def on_quote(self, quote_data):
        self.quote_data_received.append(quote_data)
    
    async def on_orderbook(self, orderbook_data):
        self.orderbook_data_received.append(orderbook_data)


class TestAlgo:
    """Test cases for the Algo abstract base class."""
    
    def test_algo_is_abstract(self):
        """Test that Algo cannot be instantiated directly."""
        with pytest.raises(TypeError):
            Algo("TestAlgo")
    
    def test_algo_initialization(self):
        """Test algorithm initialization."""
        config = {'param1': 'value1', 'param2': 42}
        algo = TestAlgoImplementation("MyAlgo", config)
        
        assert algo.name == "MyAlgo"
        assert algo.config == config
        assert algo.is_running is False
        assert algo.positions == {}
        assert algo.orders == {}
    
    def test_algo_initialization_without_config(self):
        """Test algorithm initialization without config."""
        algo = TestAlgoImplementation("SimpleAlgo")
        
        assert algo.name == "SimpleAlgo"
        assert algo.config == {}
        assert algo.is_running is False
    
    @pytest.mark.asyncio
    async def test_start_algorithm(self):
        """Test starting the algorithm."""
        algo = TestAlgoImplementation()
        
        assert algo.is_running is False
        assert algo.start_called is False
        
        await algo.start()
        
        assert algo.is_running is True
        assert algo.start_called is True
    
    @pytest.mark.asyncio
    async def test_start_already_running(self):
        """Test starting an already running algorithm."""
        algo = TestAlgoImplementation()
        
        # Start first time
        await algo.start()
        assert algo.is_running is True
        
        # Reset the flag to test second start
        algo.start_called = False
        
        # Try to start again
        await algo.start()
        
        # Should still be running but on_start shouldn't be called again
        assert algo.is_running is True
        assert algo.start_called is False
    
    @pytest.mark.asyncio
    async def test_stop_algorithm(self):
        """Test stopping the algorithm."""
        algo = TestAlgoImplementation()
        
        # Start first
        await algo.start()
        assert algo.is_running is True
        
        # Then stop
        await algo.stop()
        
        assert algo.is_running is False
        assert algo.stop_called is True
    
    @pytest.mark.asyncio
    async def test_stop_not_running(self):
        """Test stopping an algorithm that's not running."""
        algo = TestAlgoImplementation()
        
        assert algo.is_running is False
        assert algo.stop_called is False
        
        await algo.stop()
        
        # Should remain not running and on_stop shouldn't be called
        assert algo.is_running is False
        assert algo.stop_called is False
    
    @pytest.mark.asyncio
    async def test_on_tick_handling(self):
        """Test tick data handling."""
        algo = TestAlgoImplementation()
        
        tick_data = {
            'symbol': 'RELIANCE',
            'price': 2500.50,
            'volume': 1000,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        await algo.on_tick(tick_data)
        
        assert len(algo.tick_data_received) == 1
        assert algo.tick_data_received[0] == tick_data
    
    @pytest.mark.asyncio
    async def test_on_bar_handling(self):
        """Test bar data handling."""
        algo = TestAlgoImplementation()
        
        bar_data = {
            'symbol': 'TCS',
            'open': 3200.0,
            'high': 3250.0,
            'low': 3180.0,
            'close': 3220.0,
            'volume': 50000,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        await algo.on_bar(bar_data)
        
        assert len(algo.bar_data_received) == 1
        assert algo.bar_data_received[0] == bar_data
    
    @pytest.mark.asyncio
    async def test_on_fill_handling(self):
        """Test fill data handling."""
        algo = TestAlgoImplementation()
        
        fill_data = {
            'order_id': 'order_123',
            'symbol': 'INFY',
            'quantity': 100,
            'price': 1500.0,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        await algo.on_fill(fill_data)
        
        assert len(algo.fill_data_received) == 1
        assert algo.fill_data_received[0] == fill_data
    
    def test_position_management(self):
        """Test position tracking functionality."""
        algo = TestAlgoImplementation()
        
        # Initially no position
        assert algo.get_position('RELIANCE') == 0
        
        # Update position
        algo.update_position('RELIANCE', 100)
        assert algo.get_position('RELIANCE') == 100
        
        # Update position again
        algo.update_position('RELIANCE', 50)
        assert algo.get_position('RELIANCE') == 150
        
        # Reduce position
        algo.update_position('RELIANCE', -75)
        assert algo.get_position('RELIANCE') == 75
    
    def test_config_management(self):
        """Test configuration management."""
        config = {
            'fast_period': 10,
            'slow_period': 20,
            'symbol': 'NIFTY',
            'enabled': True
        }
        algo = TestAlgoImplementation(config=config)
        
        # Test getting existing config values
        assert algo.get_config('fast_period') == 10
        assert algo.get_config('slow_period') == 20
        assert algo.get_config('symbol') == 'NIFTY'
        assert algo.get_config('enabled') is True
        
        # Test getting non-existent config with default
        assert algo.get_config('non_existent', 'default_value') == 'default_value'
        
        # Test getting non-existent config without default
        assert algo.get_config('non_existent') is None
    
    def test_multiple_symbols_positions(self):
        """Test position tracking for multiple symbols."""
        algo = TestAlgoImplementation()
        
        # Update positions for different symbols
        algo.update_position('RELIANCE', 100)
        algo.update_position('TCS', 50)
        algo.update_position('INFY', -25)
        
        assert algo.get_position('RELIANCE') == 100
        assert algo.get_position('TCS') == 50
        assert algo.get_position('INFY') == -25
        assert algo.get_position('HDFC') == 0  # Not updated
    
    @pytest.mark.asyncio
    async def test_lifecycle_sequence(self):
        """Test complete algorithm lifecycle."""
        algo = TestAlgoImplementation()
        
        # Initial state
        assert algo.is_running is False
        assert algo.start_called is False
        assert algo.stop_called is False
        
        # Start algorithm
        await algo.start()
        assert algo.is_running is True
        assert algo.start_called is True
        assert algo.stop_called is False
        
        # Process some data
        await algo.on_tick({'symbol': 'TEST', 'price': 100.0})
        assert len(algo.tick_data_received) == 1
        
        # Stop algorithm
        await algo.stop()
        assert algo.is_running is False
        assert algo.start_called is True
        assert algo.stop_called is True


if __name__ == '__main__':
    pytest.main([__file__])
