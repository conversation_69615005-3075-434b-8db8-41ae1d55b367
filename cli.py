#!/usr/bin/env python3
"""
Command Line Interface for the Algorithmic Trading Framework.

This script provides command-line access to the framework's functionality
including running strategies, backtesting, and paper trading.
"""

import argparse
import asyncio
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Add framework to path
sys.path.insert(0, str(Path(__file__).parent))

from framework.utils.logging_config import setup_logging, setup_environment_logging
from framework.utils.config_loader import get_config_loader
from framework.strategies.crossover import MovingAverageCrossover
from framework.brokers.zerodha import ZerodhaBroker
from framework.blotter import Blotter


logger = logging.getLogger(__name__)


class AlgoTradeCLI:
    """Main CLI class for the algorithmic trading framework."""
    
    def __init__(self):
        self.config_loader = None
        self.broker = None
        self.blotter = None
        self.strategies = []
    
    def setup_config(self, config_file: Optional[str] = None, env_file: Optional[str] = None):
        """Setup configuration loader."""
        self.config_loader = get_config_loader(env_file, config_file)
        
        # Setup logging from config
        log_level = self.config_loader.get('log_level', 'INFO')
        log_file = self.config_loader.get('log_file')
        setup_logging(level=log_level, log_file=log_file)
        
        logger.info("Configuration loaded successfully")
    
    async def setup_broker(self, broker_name: str = 'zerodha'):
        """Setup broker connection."""
        broker_config = self.config_loader.get_broker_config(broker_name)
        
        if broker_name.lower() == 'zerodha':
            self.broker = ZerodhaBroker(broker_config)
        else:
            raise ValueError(f"Unsupported broker: {broker_name}")
        
        # Connect to broker
        connected = await self.broker.connect()
        if not connected:
            raise RuntimeError(f"Failed to connect to {broker_name}")
        
        logger.info(f"Connected to {broker_name} broker")
    
    def setup_blotter(self):
        """Setup market data blotter."""
        self.blotter = Blotter()
        logger.info("Blotter initialized")
    
    async def run_strategy(self, strategy_name: str, strategy_config: Dict[str, Any]):
        """Run a trading strategy."""
        logger.info(f"Starting strategy: {strategy_name}")
        
        # Create strategy instance
        if strategy_name.lower() == 'ma_crossover':
            strategy = MovingAverageCrossover(strategy_name, strategy_config)
        else:
            raise ValueError(f"Unknown strategy: {strategy_name}")
        
        # Subscribe strategy to market data
        self.blotter.subscribe_tick(strategy.on_tick)
        self.blotter.subscribe_bar(strategy.on_bar)
        self.blotter.subscribe_quote(strategy.on_quote)
        self.blotter.subscribe_orderbook(strategy.on_orderbook)
        
        # Start components
        await self.blotter.start()
        await strategy.start()
        
        self.strategies.append(strategy)
        
        logger.info(f"Strategy {strategy_name} started successfully")
        return strategy
    
    async def run_command(self, args):
        """Execute the run command."""
        logger.info("Starting live trading mode")
        
        # Setup components
        await self.setup_broker(args.broker)
        self.setup_blotter()
        
        # Get strategy configuration
        strategy_config = self.config_loader.get_strategy_config(args.strategy)
        if args.symbol:
            strategy_config['symbol'] = args.symbol
        if args.quantity:
            strategy_config['quantity'] = args.quantity
        
        # Run strategy
        strategy = await self.run_strategy(args.strategy, strategy_config)
        
        try:
            # Keep running until interrupted
            logger.info("Trading started. Press Ctrl+C to stop.")
            while True:
                await asyncio.sleep(1)
                
                # Check if strategy is still running
                if not strategy.is_running:
                    logger.warning("Strategy stopped unexpectedly")
                    break
        
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, stopping...")
        
        finally:
            # Cleanup
            await self.cleanup()
    
    async def backtest_command(self, args):
        """Execute the backtest command."""
        logger.info("Starting backtest mode")
        
        # TODO: Implement backtesting functionality
        # This would involve:
        # 1. Loading historical data
        # 2. Setting up a backtesting environment
        # 3. Running strategy against historical data
        # 4. Generating performance reports
        
        logger.warning("Backtesting functionality not yet implemented")
        
        print("Backtest Configuration:")
        print(f"  Strategy: {args.strategy}")
        print(f"  Start Date: {args.start_date}")
        print(f"  End Date: {args.end_date}")
        print(f"  Symbol: {args.symbol}")
        print(f"  Initial Capital: {args.capital}")
        
        # Placeholder implementation
        await asyncio.sleep(1)
        print("Backtest completed (placeholder)")
    
    async def papertrade_command(self, args):
        """Execute the paper trade command."""
        logger.info("Starting paper trading mode")
        
        # TODO: Implement paper trading functionality
        # This would involve:
        # 1. Setting up a simulated broker
        # 2. Using real market data but simulated orders
        # 3. Tracking simulated portfolio performance
        
        logger.warning("Paper trading functionality not yet implemented")
        
        print("Paper Trading Configuration:")
        print(f"  Strategy: {args.strategy}")
        print(f"  Symbol: {args.symbol}")
        print(f"  Initial Capital: {args.capital}")
        
        # Placeholder implementation
        await asyncio.sleep(1)
        print("Paper trading session completed (placeholder)")
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up resources...")
        
        # Stop strategies
        for strategy in self.strategies:
            if strategy.is_running:
                await strategy.stop()
        
        # Stop blotter
        if self.blotter and self.blotter.is_running:
            await self.blotter.stop()
        
        # Disconnect broker
        if self.broker and self.broker.is_connected:
            await self.broker.disconnect()
        
        logger.info("Cleanup completed")


def create_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Algorithmic Trading Framework CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s run --strategy ma_crossover --symbol RELIANCE --quantity 100
  %(prog)s backtest --strategy ma_crossover --start-date 2023-01-01 --end-date 2023-12-31
  %(prog)s papertrade --strategy ma_crossover --symbol NIFTY --capital 100000
        """
    )
    
    # Global arguments
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--env-file', help='Environment file path (.env)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level')
    parser.add_argument('--log-file', help='Log file path')
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Run command
    run_parser = subparsers.add_parser('run', help='Run live trading strategy')
    run_parser.add_argument('--strategy', required=True, 
                           choices=['ma_crossover'], 
                           help='Strategy to run')
    run_parser.add_argument('--broker', default='zerodha', 
                           choices=['zerodha'], 
                           help='Broker to use')
    run_parser.add_argument('--symbol', help='Trading symbol')
    run_parser.add_argument('--quantity', type=int, help='Quantity to trade')
    
    # Backtest command
    backtest_parser = subparsers.add_parser('backtest', help='Run strategy backtest')
    backtest_parser.add_argument('--strategy', required=True,
                                choices=['ma_crossover'],
                                help='Strategy to backtest')
    backtest_parser.add_argument('--start-date', required=True,
                                help='Backtest start date (YYYY-MM-DD)')
    backtest_parser.add_argument('--end-date', required=True,
                                help='Backtest end date (YYYY-MM-DD)')
    backtest_parser.add_argument('--symbol', required=True,
                                help='Symbol to backtest')
    backtest_parser.add_argument('--capital', type=float, default=100000,
                                help='Initial capital for backtest')
    
    # Paper trade command
    papertrade_parser = subparsers.add_parser('papertrade', help='Run paper trading')
    papertrade_parser.add_argument('--strategy', required=True,
                                  choices=['ma_crossover'],
                                  help='Strategy to paper trade')
    papertrade_parser.add_argument('--symbol', required=True,
                                  help='Symbol to trade')
    papertrade_parser.add_argument('--capital', type=float, default=100000,
                                  help='Initial capital for paper trading')
    
    return parser


async def main():
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Initialize CLI
    cli = AlgoTradeCLI()
    
    try:
        # Setup configuration
        cli.setup_config(args.config, args.env_file)
        
        # Override log level if specified
        if args.log_level:
            setup_logging(level=args.log_level, log_file=args.log_file)
        
        # Execute command
        if args.command == 'run':
            await cli.run_command(args)
        elif args.command == 'backtest':
            await cli.backtest_command(args)
        elif args.command == 'papertrade':
            await cli.papertrade_command(args)
        
        return 0
    
    except Exception as e:
        logger.error(f"Error executing command: {e}")
        return 1
    
    finally:
        await cli.cleanup()


if __name__ == '__main__':
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
