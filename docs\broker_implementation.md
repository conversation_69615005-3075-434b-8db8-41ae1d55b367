# Broker Implementation Guide

## Overview

This document explains the implementation of Step 2: Abstract Broker Layer + Zerodha implementation for the algorithmic trading framework.

## Architecture

### Abstract Broker Interface (`framework/broker.py`)

The `Broker` abstract base class defines the standard interface that all broker implementations must follow:

```python
from abc import ABC, abstractmethod

class Broker(ABC):
    @abstractmethod
    def connect(self) -> None
    @abstractmethod  
    def place_order(symbol: str, qty: int, order_type: str = "market", price: float | None = None) -> dict
    # ... other methods
```

**Key Methods:**
- `connect()` / `disconnect()` - Establish/close broker connection
- `place_order()` - Place buy/sell orders
- `cancel_order()` - Cancel existing orders
- `get_positions()` - Retrieve current positions
- `get_account_balance()` - Get account balance and margin info
- `get_option_chain()` - Fetch option chain data
- `start_market_ticker()` - Start real-time market data feed

### Data Models (`framework/models.py`)

**Position Model:**
```python
@dataclass
class Position:
    symbol: str
    qty: int  # Positive for long, negative for short
    avg_price: float
    side: str  # "long", "short", or "flat"
    updated_at: datetime
    
    def update_on_fill(self, qty: int, fill_price: float, side: str) -> None:
        # Updates position and recalculates average price
```

**Order Model:**
```python
@dataclass
class Order:
    order_id: str
    symbol: str
    qty: int
    order_type: str
    status: str
    price: Optional[float] = None
    created_at: datetime
    filled_at: Optional[datetime] = None
```

**Other Models:**
- `Tick` - Real-time market data
- `OHLC` - Bar/candlestick data
- `OptionChain` - Option contracts for an underlying
- `Instrument` - Tradeable instrument metadata

## ZerodhaBroker Implementation

### Dual Mode Operation

The `ZerodhaBroker` supports two modes:

1. **Mock Mode** (`mock_orders=True`):
   - Simulates order placement without real API calls
   - Maintains in-memory positions and orders
   - Perfect for testing and backtesting
   - Generates realistic mock market data

2. **Live Mode** (`mock_orders=False`):
   - Uses real Zerodha KiteConnect API
   - Requires valid API credentials
   - Places actual orders (use with caution!)

### Configuration

**Environment Variables:**
```bash
ZERODHA_API_KEY=your_api_key
ZERODHA_API_SECRET=your_api_secret  
ZERODHA_ACCESS_TOKEN=your_access_token
```

**Initialization:**
```python
# Mock mode (safe for testing)
broker = ZerodhaBroker(mock_orders=True)

# Live mode (requires credentials)
broker = ZerodhaBroker(
    api_key="your_key",
    access_token="your_token", 
    mock_orders=False
)
```

### Position Management Logic

The position update logic handles complex scenarios:

```python
def update_on_fill(self, qty: int, fill_price: float, side: str):
    if side == "buy":
        if self.qty >= 0:  # Currently long or flat
            # Calculate new average: (old_avg*old_qty + fill_price*fill_qty)/(old_qty + fill_qty)
            new_total_value = (self.avg_price * self.qty) + (fill_price * qty)
            new_total_qty = self.qty + qty
            self.avg_price = new_total_value / new_total_qty
            self.qty = new_total_qty
```

**Examples:**
- Buy 100 @ ₹2500, then buy 50 @ ₹2520 → 150 shares @ ₹2506.67 avg
- Long 100 shares, sell 30 → 70 shares (avg price unchanged)
- Long 50 shares, sell 80 → Short 30 shares @ new avg price

### Market Data Feed

**Mock Mode:**
- Generates realistic price movements using hash-based randomization
- Provides tick data every second for subscribed symbols
- Includes bid/ask spreads and volume data

**Live Mode:**
- Uses KiteTicker websocket for real-time data
- Handles connection/disconnection events
- TODO: Implement reconnection logic for production use

### Error Handling

The implementation includes comprehensive error handling:

```python
def place_order(self, symbol: str, qty: int, order_type: str = "market", price: float = None):
    if not self._is_connected:
        raise RuntimeError("Broker not connected. Call connect() first.")
    
    try:
        # API call logic
    except Exception as e:
        logger.error(f"Failed to place order: {e}")
        return {"status": "error", "message": str(e)}
```

## Testing

### Test Structure

The test suite covers:

1. **Abstract Interface Tests:**
   - Verifies `Broker` cannot be instantiated directly
   - Tests abstract method enforcement

2. **Mock Mode Tests:**
   - Complete order flow (place → fill → position update)
   - Position calculation accuracy
   - Order cancellation
   - Account balance retrieval
   - Option chain data

3. **Live Mode Tests:**
   - Skipped unless credentials are available
   - Basic connection and data retrieval
   - No actual order placement (safety)

### Running Tests

```bash
# Run all broker tests
pytest framework/tests/test_broker.py -v

# Run with live credentials (optional)
export ZERODHA_API_KEY=your_key
export ZERODHA_ACCESS_TOKEN=your_token
pytest framework/tests/test_broker.py -v
```

## Usage Examples

### Basic Order Flow

```python
from framework import ZerodhaBroker

# Initialize in mock mode
broker = ZerodhaBroker(mock_orders=True)
broker.connect()

# Place orders
buy_order = broker.place_order("RELIANCE", 100, "market", 2500.0)
sell_order = broker.place_order("RELIANCE", -30, "market", 2530.0)

# Check positions
positions = broker.get_positions()
for pos in positions:
    print(f"{pos.symbol}: {pos.qty} @ ₹{pos.avg_price}")

# Get account info
balance = broker.get_account_balance()
print(f"Available cash: ₹{balance['cash']}")
```

### Market Data Subscription

```python
def handle_tick(tick):
    print(f"{tick.symbol}: ₹{tick.last_price} at {tick.timestamp}")

broker.start_market_ticker(["RELIANCE", "TCS"], handle_tick)
```

## Production Considerations

### TODO Items for Production Use

1. **Reconnection Logic:**
   - Implement automatic websocket reconnection
   - Handle API rate limits and timeouts
   - Add circuit breaker patterns

2. **Persistence:**
   - Store orders and positions in database
   - Implement order state recovery
   - Add audit logging

3. **Risk Management:**
   - Position size limits
   - Daily loss limits
   - Order validation rules

4. **Performance:**
   - Connection pooling
   - Async/await for non-blocking operations
   - Batch order processing

### Security Notes

- Never commit API credentials to version control
- Use environment variables or secure key management
- Implement proper authentication and authorization
- Log security events and API access

## Dependencies

- `kiteconnect>=4.0.0` - Zerodha's official Python SDK
- `python-dotenv>=0.19.0` - Environment variable management
- `pytest>=7.0.0` - Testing framework

## References

- [Kite Connect Documentation](https://kite.trade/docs/pykiteconnect/v4/)
- [Zerodha API Documentation](https://kite.trade/docs/connect/v3/)
- [Python ABC Documentation](https://docs.python.org/3/library/abc.html)
