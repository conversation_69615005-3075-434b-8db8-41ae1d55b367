"""
Technical indicators implementation wrapping TA-Lib and custom indicators.

This module provides a comprehensive set of technical indicators for use
in algorithmic trading strategies, including both TA-Lib wrappers and
custom implementations.
"""

import numpy as np
import pandas as pd
from typing import Union, Tuple, Optional, List
import logging

# Note: TA-Lib import will be added when the package is installed
# import talib

logger = logging.getLogger(__name__)


class BaseIndicator:
    """Base class for all technical indicators."""
    
    def __init__(self, name: str):
        self.name = name
        self.values = []
    
    def update(self, value: float) -> Optional[float]:
        """Update indicator with new value and return current indicator value."""
        raise NotImplementedError
    
    def reset(self):
        """Reset indicator state."""
        self.values = []


class SimpleMovingAverage(BaseIndicator):
    """Simple Moving Average indicator."""
    
    def __init__(self, period: int):
        super().__init__(f"SMA_{period}")
        self.period = period
        self.prices = []
    
    def update(self, price: float) -> Optional[float]:
        """
        Update SMA with new price.
        
        Args:
            price: New price value
            
        Returns:
            Current SMA value or None if insufficient data
        """
        self.prices.append(price)
        
        if len(self.prices) > self.period:
            self.prices.pop(0)
        
        if len(self.prices) == self.period:
            sma_value = sum(self.prices) / self.period
            self.values.append(sma_value)
            return sma_value
        
        return None
    
    def calculate(self, prices: Union[List[float], np.ndarray, pd.Series]) -> np.ndarray:
        """
        Calculate SMA for a series of prices.
        
        Args:
            prices: Array of price values
            
        Returns:
            Array of SMA values
        """
        if isinstance(prices, pd.Series):
            return prices.rolling(window=self.period).mean().values
        else:
            prices_array = np.array(prices)
            return pd.Series(prices_array).rolling(window=self.period).mean().values


class ExponentialMovingAverage(BaseIndicator):
    """Exponential Moving Average indicator."""
    
    def __init__(self, period: int):
        super().__init__(f"EMA_{period}")
        self.period = period
        self.alpha = 2.0 / (period + 1)
        self.ema_value = None
    
    def update(self, price: float) -> Optional[float]:
        """
        Update EMA with new price.
        
        Args:
            price: New price value
            
        Returns:
            Current EMA value
        """
        if self.ema_value is None:
            self.ema_value = price
        else:
            self.ema_value = (price * self.alpha) + (self.ema_value * (1 - self.alpha))
        
        self.values.append(self.ema_value)
        return self.ema_value
    
    def calculate(self, prices: Union[List[float], np.ndarray, pd.Series]) -> np.ndarray:
        """
        Calculate EMA for a series of prices.
        
        Args:
            prices: Array of price values
            
        Returns:
            Array of EMA values
        """
        if isinstance(prices, pd.Series):
            return prices.ewm(span=self.period).mean().values
        else:
            return pd.Series(prices).ewm(span=self.period).mean().values


class RSI(BaseIndicator):
    """Relative Strength Index indicator."""
    
    def __init__(self, period: int = 14):
        super().__init__(f"RSI_{period}")
        self.period = period
        self.prices = []
        self.gains = []
        self.losses = []
    
    def update(self, price: float) -> Optional[float]:
        """
        Update RSI with new price.
        
        Args:
            price: New price value
            
        Returns:
            Current RSI value or None if insufficient data
        """
        self.prices.append(price)
        
        if len(self.prices) < 2:
            return None
        
        change = self.prices[-1] - self.prices[-2]
        gain = max(change, 0)
        loss = max(-change, 0)
        
        self.gains.append(gain)
        self.losses.append(loss)
        
        if len(self.gains) > self.period:
            self.gains.pop(0)
            self.losses.pop(0)
        
        if len(self.gains) == self.period:
            avg_gain = sum(self.gains) / self.period
            avg_loss = sum(self.losses) / self.period
            
            if avg_loss == 0:
                rsi_value = 100
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
            
            self.values.append(rsi_value)
            return rsi_value
        
        return None
    
    def calculate(self, prices: Union[List[float], np.ndarray, pd.Series]) -> np.ndarray:
        """
        Calculate RSI for a series of prices.
        
        Args:
            prices: Array of price values
            
        Returns:
            Array of RSI values
        """
        # TODO: Use TA-Lib when available
        # return talib.RSI(np.array(prices), timeperiod=self.period)
        
        # Custom implementation
        prices_series = pd.Series(prices)
        delta = prices_series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.values


class MACD(BaseIndicator):
    """Moving Average Convergence Divergence indicator."""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        super().__init__(f"MACD_{fast_period}_{slow_period}_{signal_period}")
        self.fast_ema = ExponentialMovingAverage(fast_period)
        self.slow_ema = ExponentialMovingAverage(slow_period)
        self.signal_ema = ExponentialMovingAverage(signal_period)
        self.macd_line = []
        self.signal_line = []
        self.histogram = []
    
    def update(self, price: float) -> Optional[Tuple[float, float, float]]:
        """
        Update MACD with new price.
        
        Args:
            price: New price value
            
        Returns:
            Tuple of (MACD line, Signal line, Histogram) or None if insufficient data
        """
        fast_ema = self.fast_ema.update(price)
        slow_ema = self.slow_ema.update(price)
        
        if fast_ema is None or slow_ema is None:
            return None
        
        macd_value = fast_ema - slow_ema
        self.macd_line.append(macd_value)
        
        signal_value = self.signal_ema.update(macd_value)
        if signal_value is None:
            return None
        
        self.signal_line.append(signal_value)
        histogram_value = macd_value - signal_value
        self.histogram.append(histogram_value)
        
        return macd_value, signal_value, histogram_value
    
    def calculate(self, prices: Union[List[float], np.ndarray, pd.Series]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate MACD for a series of prices.
        
        Args:
            prices: Array of price values
            
        Returns:
            Tuple of (MACD line, Signal line, Histogram) arrays
        """
        # TODO: Use TA-Lib when available
        # macd, signal, histogram = talib.MACD(np.array(prices))
        # return macd, signal, histogram
        
        # Custom implementation
        fast_ema = self.fast_ema.calculate(prices)
        slow_ema = self.slow_ema.calculate(prices)
        macd_line = fast_ema - slow_ema
        
        signal_ema = ExponentialMovingAverage(self.signal_ema.period)
        signal_line = signal_ema.calculate(macd_line)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram


class BollingerBands(BaseIndicator):
    """Bollinger Bands indicator."""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        super().__init__(f"BB_{period}_{std_dev}")
        self.period = period
        self.std_dev = std_dev
        self.prices = []
    
    def update(self, price: float) -> Optional[Tuple[float, float, float]]:
        """
        Update Bollinger Bands with new price.
        
        Args:
            price: New price value
            
        Returns:
            Tuple of (Upper band, Middle band, Lower band) or None if insufficient data
        """
        self.prices.append(price)
        
        if len(self.prices) > self.period:
            self.prices.pop(0)
        
        if len(self.prices) == self.period:
            middle_band = sum(self.prices) / self.period
            variance = sum((p - middle_band) ** 2 for p in self.prices) / self.period
            std_deviation = variance ** 0.5
            
            upper_band = middle_band + (self.std_dev * std_deviation)
            lower_band = middle_band - (self.std_dev * std_deviation)
            
            return upper_band, middle_band, lower_band
        
        return None
    
    def calculate(self, prices: Union[List[float], np.ndarray, pd.Series]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate Bollinger Bands for a series of prices.
        
        Args:
            prices: Array of price values
            
        Returns:
            Tuple of (Upper band, Middle band, Lower band) arrays
        """
        # TODO: Use TA-Lib when available
        # upper, middle, lower = talib.BBANDS(np.array(prices), timeperiod=self.period, nbdevup=self.std_dev, nbdevdn=self.std_dev)
        # return upper, middle, lower
        
        # Custom implementation
        prices_series = pd.Series(prices)
        middle_band = prices_series.rolling(window=self.period).mean()
        std_deviation = prices_series.rolling(window=self.period).std()
        upper_band = middle_band + (self.std_dev * std_deviation)
        lower_band = middle_band - (self.std_dev * std_deviation)
        
        return upper_band.values, middle_band.values, lower_band.values


class Stochastic(BaseIndicator):
    """Stochastic Oscillator indicator."""
    
    def __init__(self, k_period: int = 14, d_period: int = 3):
        super().__init__(f"STOCH_{k_period}_{d_period}")
        self.k_period = k_period
        self.d_period = d_period
        self.highs = []
        self.lows = []
        self.closes = []
        self.k_values = []
    
    def update(self, high: float, low: float, close: float) -> Optional[Tuple[float, float]]:
        """
        Update Stochastic with new OHLC data.
        
        Args:
            high: High price
            low: Low price
            close: Close price
            
        Returns:
            Tuple of (%K, %D) or None if insufficient data
        """
        self.highs.append(high)
        self.lows.append(low)
        self.closes.append(close)
        
        if len(self.highs) > self.k_period:
            self.highs.pop(0)
            self.lows.pop(0)
            self.closes.pop(0)
        
        if len(self.highs) == self.k_period:
            highest_high = max(self.highs)
            lowest_low = min(self.lows)
            current_close = self.closes[-1]
            
            if highest_high == lowest_low:
                k_value = 50.0
            else:
                k_value = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100
            
            self.k_values.append(k_value)
            
            if len(self.k_values) > self.d_period:
                self.k_values.pop(0)
            
            if len(self.k_values) == self.d_period:
                d_value = sum(self.k_values) / self.d_period
                return k_value, d_value
        
        return None
