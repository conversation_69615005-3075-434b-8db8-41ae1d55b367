"""
Unit tests for trading strategies.

This module contains tests for concrete strategy implementations
like MovingAverageCrossover.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from framework.strategies.crossover import MovingAverageCrossover


class TestMovingAverageCrossover:
    """Test cases for MovingAverageCrossover strategy."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = {
            'fast_period': 5,
            'slow_period': 10,
            'ma_type': 'SMA',
            'symbol': 'TESTSTOCK',
            'quantity': 100
        }
        self.strategy = MovingAverageCrossover("TestMA", self.config)
    
    def test_strategy_initialization(self):
        """Test strategy initialization with config."""
        assert self.strategy.name == "TestMA"
        assert self.strategy.fast_period == 5
        assert self.strategy.slow_period == 10
        assert self.strategy.ma_type == 'SMA'
        assert self.strategy.symbol == 'TESTSTOCK'
        assert self.strategy.quantity == 100
        assert self.strategy.is_running is False
    
    def test_strategy_initialization_defaults(self):
        """Test strategy initialization with default values."""
        strategy = MovingAverageCrossover()
        
        assert strategy.name == "MA_Crossover"
        assert strategy.fast_period == 10
        assert strategy.slow_period == 20
        assert strategy.ma_type == 'SMA'
        assert strategy.symbol == 'RELIANCE'
        assert strategy.quantity == 100
    
    def test_strategy_initialization_ema(self):
        """Test strategy initialization with EMA."""
        config = {'ma_type': 'EMA', 'fast_period': 12, 'slow_period': 26}
        strategy = MovingAverageCrossover(config=config)
        
        assert strategy.ma_type == 'EMA'
        assert strategy.fast_period == 12
        assert strategy.slow_period == 26
    
    @pytest.mark.asyncio
    async def test_on_start(self):
        """Test strategy start lifecycle."""
        await self.strategy.on_start()
        
        assert self.strategy.last_signal is None
    
    @pytest.mark.asyncio
    async def test_on_stop(self):
        """Test strategy stop lifecycle."""
        # Set up a position
        self.strategy.update_position('TESTSTOCK', 100)
        
        await self.strategy.on_stop()
        
        # Strategy should handle position closing
        # (In real implementation, this would place closing orders)
    
    @pytest.mark.asyncio
    async def test_on_tick_wrong_symbol(self):
        """Test tick handling for wrong symbol."""
        tick_data = {
            'symbol': 'WRONGSTOCK',
            'price': 100.0,
            'volume': 1000,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        # Should not process tick for wrong symbol
        await self.strategy.on_tick(tick_data)
        
        assert self.strategy.current_fast_ma is None
        assert self.strategy.current_slow_ma is None
    
    @pytest.mark.asyncio
    async def test_on_tick_correct_symbol(self):
        """Test tick handling for correct symbol."""
        tick_data = {
            'symbol': 'TESTSTOCK',
            'price': 100.0,
            'volume': 1000,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        await self.strategy.on_tick(tick_data)
        
        # Moving averages should be updated (though may still be None if insufficient data)
        # This depends on the moving average implementation
    
    @pytest.mark.asyncio
    async def test_on_bar_processing(self):
        """Test bar data processing."""
        bar_data = {
            'symbol': 'TESTSTOCK',
            'open': 95.0,
            'high': 105.0,
            'low': 90.0,
            'close': 100.0,
            'volume': 50000,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        await self.strategy.on_bar(bar_data)
        
        # Should process the close price for moving averages
    
    @pytest.mark.asyncio
    async def test_on_fill_processing(self):
        """Test fill data processing."""
        fill_data = {
            'order_id': 'order_123',
            'symbol': 'TESTSTOCK',
            'quantity': 100,
            'price': 100.0,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        initial_position = self.strategy.get_position('TESTSTOCK')
        
        await self.strategy.on_fill(fill_data)
        
        # Position should be updated
        assert self.strategy.get_position('TESTSTOCK') == initial_position + 100
    
    @pytest.mark.asyncio
    async def test_on_fill_wrong_symbol(self):
        """Test fill processing for wrong symbol."""
        fill_data = {
            'order_id': 'order_123',
            'symbol': 'WRONGSTOCK',
            'quantity': 100,
            'price': 100.0,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        initial_position = self.strategy.get_position('TESTSTOCK')
        
        await self.strategy.on_fill(fill_data)
        
        # Position should not change for wrong symbol
        assert self.strategy.get_position('TESTSTOCK') == initial_position
    
    def test_moving_average_updates(self):
        """Test moving average update mechanism."""
        # Test with insufficient data
        self.strategy._update_moving_averages(100.0)
        
        # With only one data point, MAs might still be None
        # This depends on the moving average implementation
        
        # Add more data points
        prices = [100.0, 101.0, 102.0, 103.0, 104.0, 105.0]
        for price in prices:
            self.strategy._update_moving_averages(price)
        
        # After enough data points, we should have MA values
        # (Exact behavior depends on MA implementation)
    
    @pytest.mark.asyncio
    async def test_signal_generation_insufficient_data(self):
        """Test signal generation with insufficient data."""
        # With no previous MA values, no signals should be generated
        await self.strategy._check_signals()
        
        assert self.strategy.last_signal is None
    
    @pytest.mark.asyncio
    @patch.object(MovingAverageCrossover, '_generate_buy_signal')
    async def test_bullish_crossover_detection(self, mock_buy_signal):
        """Test detection of bullish crossover."""
        # Set up crossover scenario
        self.strategy.last_fast_ma = 99.0
        self.strategy.last_slow_ma = 100.0
        self.strategy.current_fast_ma = 101.0
        self.strategy.current_slow_ma = 100.0
        
        await self.strategy._check_signals()
        
        # Should generate buy signal
        mock_buy_signal.assert_called_once()
    
    @pytest.mark.asyncio
    @patch.object(MovingAverageCrossover, '_generate_sell_signal')
    async def test_bearish_crossover_detection(self, mock_sell_signal):
        """Test detection of bearish crossover."""
        # Set up crossover scenario
        self.strategy.last_fast_ma = 101.0
        self.strategy.last_slow_ma = 100.0
        self.strategy.current_fast_ma = 99.0
        self.strategy.current_slow_ma = 100.0
        
        await self.strategy._check_signals()
        
        # Should generate sell signal
        mock_sell_signal.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_no_crossover_no_signal(self):
        """Test that no signals are generated without crossover."""
        # Set up non-crossover scenario
        self.strategy.last_fast_ma = 101.0
        self.strategy.last_slow_ma = 100.0
        self.strategy.current_fast_ma = 102.0
        self.strategy.current_slow_ma = 100.5
        
        with patch.object(self.strategy, '_generate_buy_signal') as mock_buy, \
             patch.object(self.strategy, '_generate_sell_signal') as mock_sell:
            
            await self.strategy._check_signals()
            
            # No signals should be generated
            mock_buy.assert_not_called()
            mock_sell.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_buy_signal_generation(self):
        """Test buy signal generation."""
        await self.strategy._generate_buy_signal()
        
        assert self.strategy.last_signal == 'BUY'
    
    @pytest.mark.asyncio
    async def test_sell_signal_generation(self):
        """Test sell signal generation."""
        await self.strategy._generate_sell_signal()
        
        assert self.strategy.last_signal == 'SELL'
    
    def test_get_strategy_state(self):
        """Test getting strategy state."""
        # Set up some state
        self.strategy.current_fast_ma = 101.0
        self.strategy.current_slow_ma = 100.0
        self.strategy.last_signal = 'BUY'
        self.strategy.update_position('TESTSTOCK', 100)
        self.strategy.is_running = True
        
        state = self.strategy.get_strategy_state()
        
        assert state['symbol'] == 'TESTSTOCK'
        assert state['fast_ma'] == 101.0
        assert state['slow_ma'] == 100.0
        assert state['last_signal'] == 'BUY'
        assert state['position'] == 100
        assert state['is_running'] is True
    
    @pytest.mark.asyncio
    async def test_complete_workflow(self):
        """Test complete strategy workflow with price data."""
        # Start strategy
        await self.strategy.start()
        assert self.strategy.is_running is True
        
        # Feed price data to build up moving averages
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        
        for i, price in enumerate(prices):
            bar_data = {
                'symbol': 'TESTSTOCK',
                'open': price - 0.5,
                'high': price + 0.5,
                'low': price - 1.0,
                'close': price,
                'volume': 1000,
                'timestamp': f'2024-01-01T10:{i:02d}:00Z'
            }
            await self.strategy.on_bar(bar_data)
        
        # Stop strategy
        await self.strategy.stop()
        assert self.strategy.is_running is False


if __name__ == '__main__':
    pytest.main([__file__])
